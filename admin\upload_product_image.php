<?php
require_once '../includes/error_handler.php';
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';
require_once '../includes/image_handler.php';

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get products
$stmt = $pdo->query("SELECT id, name, image FROM products ORDER BY name");
$products = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Image Upload</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .product-card {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }
        .product-image-preview {
            width: 200px;
            height: 150px;
            margin: 0 auto 15px;
            border-radius: 10px;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .product-image-preview img,
        .product-image-preview object {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .product-name {
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .upload-form {
            margin-top: 15px;
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin-bottom: 10px;
        }
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .file-input-label {
            display: inline-block;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-input-label:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .upload-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.3s ease;
        }
        .upload-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .upload-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .supported-formats {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .format-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .format-tag {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .fallback-styles {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 10px;
        }
        .fallback-icon {
            font-size: 48px;
            display: block;
            margin-bottom: 10px;
        }
        .fallback-text {
            font-size: 12px;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📸 Product Image Upload</h1>
            <p>Upload images for your products - All formats supported!</p>
        </div>

        <div class="supported-formats">
            <h3>🎨 Supported Image Formats:</h3>
            <div class="format-list">
                <span class="format-tag">JPG/JPEG</span>
                <span class="format-tag">PNG</span>
                <span class="format-tag">GIF</span>
                <span class="format-tag">WebP</span>
                <span class="format-tag">BMP</span>
                <span class="format-tag">SVG</span>
                <span class="format-tag">ICO</span>
                <span class="format-tag">TIFF</span>
            </div>
            <p style="margin-top: 10px; color: #666; font-size: 14px;">
                Maximum file size: 10MB | Images will be automatically resized to 800x600px (except SVG)
            </p>
        </div>

        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <div class="product-image-preview">
                        <?php echo ImageHandler::getImageHTML($product['image'], $product['name'], '', 'width: 100%; height: 100%; object-fit: cover;', $product['id']); ?>
                    </div>
                    
                    <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                    
                    <div class="upload-form">
                        <div class="file-input-wrapper">
                            <input type="file" class="file-input" id="file-<?php echo $product['id']; ?>" 
                                   accept="image/*,.svg" onchange="previewImage(this, <?php echo $product['id']; ?>)">
                            <label for="file-<?php echo $product['id']; ?>" class="file-input-label">
                                📁 Choose Image
                            </label>
                        </div>
                        <button class="upload-btn" onclick="uploadImage(<?php echo $product['id']; ?>)" disabled>
                            📤 Upload
                        </button>
                        <div class="message" id="message-<?php echo $product['id']; ?>" style="display: none;"></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script>
        function previewImage(input, productId) {
            const file = input.files[0];
            const uploadBtn = input.parentElement.nextElementSibling;
            const messageDiv = document.getElementById('message-' + productId);
            
            if (file) {
                // Enable upload button
                uploadBtn.disabled = false;
                
                // Show file info
                messageDiv.style.display = 'block';
                messageDiv.className = 'message';
                messageDiv.innerHTML = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                
                // Preview image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = input.closest('.product-card').querySelector('.product-image-preview');
                    
                    if (file.type === 'image/svg+xml') {
                        preview.innerHTML = `<object data="${e.target.result}" type="image/svg+xml" style="width: 100%; height: 100%;"></object>`;
                    } else {
                        preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">`;
                    }
                };
                reader.readAsDataURL(file);
            } else {
                uploadBtn.disabled = true;
                messageDiv.style.display = 'none';
            }
        }

        function uploadImage(productId) {
            const fileInput = document.getElementById('file-' + productId);
            const uploadBtn = fileInput.parentElement.nextElementSibling;
            const messageDiv = document.getElementById('message-' + productId);
            
            if (!fileInput.files[0]) {
                showMessage(messageDiv, 'Please select an image first', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            formData.append('product_id', productId);
            formData.append('action', 'upload_image');

            // Show loading
            uploadBtn.disabled = true;
            uploadBtn.textContent = '⏳ Uploading...';

            fetch('../includes/image_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(messageDiv, data.message, 'success');
                    // Refresh page after 2 seconds
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showMessage(messageDiv, data.message, 'error');
                }
            })
            .catch(error => {
                showMessage(messageDiv, 'Upload failed: ' + error.message, 'error');
            })
            .finally(() => {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '📤 Upload';
            });
        }

        function showMessage(element, message, type) {
            element.style.display = 'block';
            element.className = 'message ' + type;
            element.textContent = message;
        }
    </script>
</body>
</html>
