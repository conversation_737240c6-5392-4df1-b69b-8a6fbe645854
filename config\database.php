<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'dokan_db');

// Create connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Check if it's a database not found error
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        die("
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background: #f9f9f9;'>
            <h2 style='color: #d32f2f;'>ডাটাবেস পাওয়া যায়নি!</h2>
            <p>দয়া করে প্রথমে ডাটাবেস সেটআপ করুন:</p>
            <ol>
                <li>XAMPP চালু করুন এবং MySQL সার্ভিস স্টার্ট করুন</li>
                <li><a href='setup.php' style='color: #1976d2; text-decoration: none; font-weight: bold;'>এই লিংকে ক্লিক করে ডাটাবেস সেটআপ করুন</a></li>
                <li>অথবা phpMyAdmin এ গিয়ে 'dokan_db' নামে একটি ডাটাবেস তৈরি করুন</li>
            </ol>
            <p style='color: #666; font-size: 14px;'>এরর: " . $e->getMessage() . "</p>
        </div>
        ");
    } else {
        die("Connection failed: " . $e->getMessage());
    }
}

// Site configuration
define('SITE_URL', 'http://localhost/dokan/');
define('SITE_NAME', 'আমার দোকান');
define('UPLOAD_PATH', 'uploads/products/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Session configuration is now handled in config/session.php

// Utility functions moved to includes/function_loader.php

if (!function_exists('redirect')) {
    function redirect($url) {
        header("Location: " . $url);
        exit();
    }
}

if (!function_exists('upload_image')) {
    function upload_image($file, $target_dir = UPLOAD_PATH) {
    // Create directory if it doesn't exist
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }

    $target_file = $target_dir . basename($file["name"]);
    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));

    // Check if image file is a actual image or fake image (skip for SVG)
    if($imageFileType !== 'svg') {
        $check = getimagesize($file["tmp_name"]);
        if($check === false) {
            return ["success" => false, "message" => "ফাইলটি একটি ছবি নয়।"];
        }
    } else {
        // For SVG files, check if it's valid XML
        $svgContent = file_get_contents($file["tmp_name"]);
        if($svgContent === false || strpos($svgContent, '<svg') === false) {
            return ["success" => false, "message" => "ফাইলটি একটি বৈধ SVG ছবি নয়।"];
        }
    }
    
    // Check file size
    if ($file["size"] > MAX_FILE_SIZE) {
        return ["success" => false, "message" => "ফাইলটি খুব বড়।"];
    }
    
    // Allow certain file formats
    $allowedTypes = ["jpg", "jpeg", "png", "gif", "webp", "bmp", "svg", "ico", "tiff", "tif"];
    if(!in_array($imageFileType, $allowedTypes)) {
        return ["success" => false, "message" => "অসমর্থিত ফাইল ফরমেট। সাপোর্টেড ফরমেট: JPG, JPEG, PNG, GIF, WebP, BMP, SVG, ICO, TIFF"];
    }
    
    // Generate unique filename
    $unique_name = uniqid() . '.' . $imageFileType;
    $target_file = $target_dir . $unique_name;
    
        if (move_uploaded_file($file["tmp_name"], $target_file)) {
            return ["success" => true, "filename" => $unique_name];
        } else {
            return ["success" => false, "message" => "ফাইল আপলোডে সমস্যা হয়েছে।"];
        }
    }
}

if (!function_exists('get_user_ip')) {
    function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
}


?>
