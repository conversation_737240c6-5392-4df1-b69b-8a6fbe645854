-- Sample products for different categories
-- Make sure to run this after the main database setup

-- ইলেকট্রনিক্স ক্যাটাগরির পণ্য
INSERT INTO products (name, description, price, discount_price, category_id, sku, stock_quantity, featured) VALUES 
('Samsung Galaxy A54', 'Samsung Galaxy A54 5G স্মার্টফোন, 8GB RAM, 128GB Storage', 45000.00, 42000.00, 1, 'ELEC-SAM-A54-001', 25, 1),
('iPhone 14', 'Apple iPhone 14, 128GB, সব রঙে পাওয়া যায়', 95000.00, 90000.00, 1, 'ELEC-APL-IP14-001', 15, 1),
('Dell Inspiron 15', 'Dell Inspiron 15 3000 ল্যাপটপ, Intel Core i5, 8GB RAM, 512GB SSD', 65000.00, 62000.00, 1, 'ELEC-DEL-INS15-001', 10, 0),
('Sony 43" Smart TV', 'Sony Bravia 43 ইঞ্চি 4K Smart LED TV', 55000.00, 52000.00, 1, 'ELEC-SON-TV43-001', 8, 1),
('JBL Bluetooth Speaker', 'JBL Charge 5 Portable Bluetooth Speaker', 12000.00, 11000.00, 1, 'ELEC-JBL-CHG5-001', 30, 0),
('Canon DSLR Camera', 'Canon EOS 1500D DSLR Camera with 18-55mm Lens', 42000.00, 40000.00, 1, 'ELEC-CAN-1500D-001', 12, 1),
('HP Printer', 'HP DeskJet 2723 All-in-One Wireless Printer', 8500.00, 8000.00, 1, 'ELEC-HP-DJ2723-001', 20, 0),
('Xiaomi Power Bank', 'Xiaomi Mi Power Bank 3 20000mAh', 2500.00, 2200.00, 1, 'ELEC-XIA-PB20K-001', 50, 0);

-- ফ্যাশন ক্যাটাগরির পণ্য
INSERT INTO products (name, description, price, discount_price, category_id, sku, stock_quantity, featured) VALUES 
('পুরুষদের কটন শার্ট', 'উন্নত মানের কটন ফর্মাল শার্ট, বিভিন্ন রঙে', 1500.00, 1200.00, 2, 'FASH-MEN-SHIRT-001', 40, 1),
('মহিলাদের কুর্তি', 'ঐতিহ্যবাহী বাংলাদেশী কুর্তি, হ্যান্ড এমব্রয়ডারি', 2500.00, 2200.00, 2, 'FASH-WOM-KURTI-001', 35, 1),
('জিন্স প্যান্ট', 'Levi\'s 511 Slim Fit Jeans, সব সাইজে', 4500.00, 4000.00, 2, 'FASH-LEV-JEANS-001', 25, 0),
('স্নিকার্স জুতা', 'Adidas Ultraboost 22 Running Shoes', 12000.00, 11000.00, 2, 'FASH-ADI-UB22-001', 18, 1),
('হ্যান্ডব্যাগ', 'চামড়ার হ্যান্ডব্যাগ, মহিলাদের জন্য', 3500.00, 3000.00, 2, 'FASH-BAG-HAND-001', 22, 0),
('পুরুষদের টি-শার্ট', 'কটন রাউন্ড নেক টি-শার্ট, বিভিন্ন ডিজাইন', 800.00, 650.00, 2, 'FASH-MEN-TSHIRT-001', 60, 0),
('মহিলাদের শাড়ি', 'ঢাকাই জামদানি শাড়ি, হস্তনির্মিত', 8500.00, 8000.00, 2, 'FASH-WOM-SAREE-001', 15, 1),
('বেল্ট', 'চামড়ার বেল্ট, পুরুষ ও মহিলা উভয়ের জন্য', 1200.00, 1000.00, 2, 'FASH-BELT-LEATH-001', 45, 0);

-- বই ক্যাটাগরির পণ্য
INSERT INTO products (name, description, price, discount_price, category_id, sku, stock_quantity, featured) VALUES 
('হুমায়ূন আহমেদের উপন্যাস', 'হিমু সিরিজের সম্পূর্ণ সংগ্রহ', 1500.00, 1300.00, 3, 'BOOK-HUM-HIMU-001', 30, 1),
('প্রোগ্রামিং বই', 'জাভাস্ক্রিপ্ট শেখার সহজ পদ্ধতি', 800.00, 700.00, 3, 'BOOK-PROG-JS-001', 25, 0),
('ইসলামিক বই', 'কুরআন শরীফ বাংলা অনুবাদসহ', 1200.00, 1000.00, 3, 'BOOK-ISL-QURAN-001', 40, 1),
('রান্নার বই', 'বাংলা রান্নার রেসিপি সংগ্রহ', 600.00, 500.00, 3, 'BOOK-COOK-BANG-001', 35, 0),
('শিশুদের বই', 'ছোটদের গল্পের বই, রঙিন ছবিসহ', 400.00, 350.00, 3, 'BOOK-CHILD-STORY-001', 50, 0),
('ইতিহাস বই', 'বাংলাদেশের মুক্তিযুদ্ধের ইতিহাস', 900.00, 800.00, 3, 'BOOK-HIST-1971-001', 20, 1),
('কবিতার বই', 'রবীন্দ্রনাথ ঠাকুরের কবিতা সংগ্রহ', 700.00, 600.00, 3, 'BOOK-POET-RABI-001', 28, 0),
('বিজ্ঞান বই', 'পদার্থবিজ্ঞানের মূল কথা', 1100.00, 950.00, 3, 'BOOK-SCI-PHYS-001', 22, 0);

-- খাবার ক্যাটাগরির পণ্য
INSERT INTO products (name, description, price, discount_price, category_id, sku, stock_quantity, featured) VALUES 
('বাসমতি চাল', 'উন্নত মানের বাসমতি চাল, 5 কেজি প্যাকেট', 650.00, 600.00, 4, 'FOOD-RICE-BASM-001', 100, 1),
('মধু', 'খাঁটি সুন্দরবনের মধু, 500 গ্রাম', 800.00, 750.00, 4, 'FOOD-HONEY-SUND-001', 45, 1),
('ডাল', 'মসুর ডাল, 1 কেজি প্যাকেট', 120.00, 110.00, 4, 'FOOD-DAL-MASUR-001', 80, 0),
('তেল', 'সয়াবিন তেল, 1 লিটার বোতল', 180.00, 170.00, 4, 'FOOD-OIL-SOYA-001', 60, 0),
('চিনি', 'সাদা চিনি, 1 কেজি প্যাকেট', 85.00, 80.00, 4, 'FOOD-SUGAR-WHITE-001', 120, 0),
('বিস্কুট', 'টাইগার বিস্কুট, 200 গ্রাম প্যাকেট', 45.00, 40.00, 4, 'FOOD-BISC-TIGER-001', 200, 0),
('চা পাতা', 'ব্রুক বন্ড তাজা চা, 500 গ্রাম', 320.00, 300.00, 4, 'FOOD-TEA-BB-001', 75, 1),
('নুডলস', 'ম্যাগি নুডলস, 4 প্যাক', 160.00, 150.00, 4, 'FOOD-NOODLE-MAGGI-001', 90, 0);

-- স্বাস্থ্য ও সৌন্দর্য ক্যাটাগরির পণ্য
INSERT INTO products (name, description, price, discount_price, category_id, sku, stock_quantity, featured) VALUES 
('ফেস ওয়াশ', 'নিভিয়া ফেস ওয়াশ, সব ধরনের ত্বকের জন্য', 350.00, 320.00, 5, 'HEALTH-NIVEA-FW-001', 40, 1),
('শ্যাম্পু', 'হেড অ্যান্ড শোল্ডার্স অ্যান্টি-ড্যান্ড্রাফ শ্যাম্পু', 450.00, 420.00, 5, 'HEALTH-HNS-SHAM-001', 35, 0),
('টুথপেস্ট', 'কোলগেট টোটাল টুথপেস্ট, 150 গ্রাম', 180.00, 160.00, 5, 'HEALTH-COL-TP-001', 60, 0),
('সানস্ক্রিন', 'লোরিয়াল সানস্ক্রিন SPF 50+', 850.00, 800.00, 5, 'HEALTH-LOR-SS-001', 25, 1),
('পারফিউম', 'CK One পারফিউম, 100ml', 3500.00, 3200.00, 5, 'HEALTH-CK-PERF-001', 18, 1),
('হ্যান্ড স্যানিটাইজার', 'ডেটল হ্যান্ড স্যানিটাইজার, 200ml', 120.00, 110.00, 5, 'HEALTH-DET-HS-001', 80, 0),
('ভিটামিন ট্যাবলেট', 'মাল্টিভিটামিন ট্যাবলেট, 30 পিস', 450.00, 400.00, 5, 'HEALTH-VIT-MULTI-001', 50, 0),
('বডি লোশন', 'ভ্যাসলিন বডি লোশন, 400ml', 280.00, 250.00, 5, 'HEALTH-VAS-BL-001', 45, 0);

-- Update featured products randomly
UPDATE products SET featured = 1 WHERE id IN (1, 4, 6, 9, 11, 16, 19, 25, 27, 33, 37);

-- Add some stock variations
UPDATE products SET stock_quantity = 0 WHERE id IN (3, 7, 15, 23, 31); -- Some out of stock items
UPDATE products SET stock_quantity = FLOOR(RAND() * 50) + 10 WHERE stock_quantity > 50; -- Random stock for high stock items
