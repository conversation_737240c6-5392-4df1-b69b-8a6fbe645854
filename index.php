<?php
require_once 'config/session.php';
require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অনলাইন দোকান</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <h2>স্বাগতম আমাদের অনলাইন দোকানে</h2>
                    <p>সেরা মানের পণ্য, সাশ্রয়ী দামে। আমাদের কাছে পাবেন বিশ্বস্ত ও গুণগত পণ্যসামগ্রী।</p>
                    <div class="hero-buttons">
                        <a href="products.php" class="btn btn-primary">পণ্য দেখুন</a>
                        <a href="salesman/login.php" class="btn btn-secondary">
                            <i class="fas fa-user-tie"></i> সেলসম্যান লগইন
                        </a>
                    </div>
                </div>
            </section>

            <!-- Featured Products -->
            <section class="featured-products">
                <h3>বিশেষ পণ্যসমূহ</h3>

                <!-- Test Button for Image Preview -->
                <div style="text-align: center; margin-bottom: 1rem;">
                    <button onclick="testImagePreview()" class="btn btn-secondary" style="margin-right: 1rem;">
                        🖼️ ইমেজ প্রিভিউ টেস্ট
                    </button>
                    <button onclick="checkImagePreviewFunction()" class="btn btn-secondary">
                        🔍 ফাংশন চেক
                    </button>
                </div>

                <div class="products-grid" id="featured-products">
                    <!-- Products will be loaded via AJAX -->
                </div>
            </section>

            <!-- Categories -->
            <section class="categories">
                <div class="section-header">
                    <h3><i class="fas fa-th-large"></i> ক্যাটেগরি</h3>
                    <div class="section-actions">
                        <p>আপনার পছন্দের পণ্যের ক্যাটেগরি নির্বাচন করুন</p>
                        <a href="category_products.php" class="btn btn-outline">সব ক্যাটাগরি দেখুন</a>
                    </div>
                </div>
                <div class="categories-container">
                    <div class="categories-grid" id="categories-list">
                        <!-- Categories will be loaded via AJAX -->
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>যোগাযোগ</h4>
                    <p><i class="fas fa-phone"></i> +880 1234567890</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>সেবা</h4>
                    <ul>
                        <li><a href="#">ডেলিভারি তথ্য</a></li>
                        <li><a href="#">রিটার্ন পলিসি</a></li>
                        <li><a href="#">সাহায্য</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>আমাদের সম্পর্কে</h4>
                    <p>আমরা একটি বিশ্বস্ত অনলাইন দোকান যা গুণগত পণ্য সরবরাহ করে।</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        // Set login status for JavaScript
        const isLoggedIn = <?php echo isset($_SESSION['customer_id']) ? 'true' : 'false'; ?>;
    </script>
    <script src="assets/js/main.js"></script>
    <script>
        // Test functions for image preview
        function testImagePreview() {
            console.log('Test button clicked');
            if (typeof showImagePreview === 'function') {
                showImagePreview('assets/images/demo-product.svg', 'টেস্ট পণ্য');
            } else {
                alert('showImagePreview ফাংশন পাওয়া যায়নি!');
            }
        }

        function checkImagePreviewFunction() {
            const status = {
                'jQuery': typeof $ !== 'undefined',
                'showImagePreview': typeof showImagePreview === 'function',
                'closeImagePreview': typeof closeImagePreview === 'function',
                'Modal exists': $('#imagePreviewModal').length > 0
            };

            console.log('Function check:', status);
            alert('Function Status:\n' + Object.entries(status).map(([key, value]) => `${key}: ${value ? '✅' : '❌'}`).join('\n'));
        }

        // Load featured products on page load
        $(document).ready(function() {
            loadFeaturedProducts();
            loadCategories();
            updateCartCount();
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
