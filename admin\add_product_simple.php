<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';
require_once '../config/functions.php';

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_product'])) {
    try {
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $price = floatval($_POST['price'] ?? 0);
        $stock_quantity = intval($_POST['stock_quantity'] ?? 0);
        $category_id = intval($_POST['category_id'] ?? 0);
        
        // Basic validation
        if (empty($name) || empty($price) || empty($category_id)) {
            throw new Exception('নাম, দাম এবং ক্যাটাগরি আবশ্যক।');
        }
        
        if ($price <= 0) {
            throw new Exception('দাম অবশ্যই ০ এর চেয়ে বেশি হতে হবে।');
        }
        
        // Generate simple SKU
        $sku = 'PROD-' . $category_id . '-' . time();
        
        // Handle image upload
        $image_filename = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/products/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $image_filename = 'product_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                $target_path = $upload_dir . $image_filename;
                
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $target_path)) {
                    throw new Exception('ছবি আপলোড করতে সমস্যা হয়েছে।');
                }
            } else {
                throw new Exception('অসমর্থিত ছবির ফরমেট।');
            }
        }
        
        // Insert product
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, sku, stock_quantity, image, status, featured, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', 0, NOW())");
        $result = $stmt->execute([$name, $description, $price, $category_id, $sku, $stock_quantity, $image_filename]);
        
        if ($result) {
            $message = 'পণ্য সফলভাবে যোগ করা হয়েছে!';
            $messageType = 'success';
            
            // Clear form data
            $_POST = [];
        } else {
            throw new Exception('পণ্য যোগ করতে সমস্যা হয়েছে।');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
    if (empty($message)) {
        $message = 'ক্যাটাগরি লোড করতে সমস্যা হয়েছে।';
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য যোগ করুন - সরল ভার্সন</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .back-btn {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header" style="position: relative;">
            <a href="dashboard.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> ড্যাশবোর্ড
            </a>
            <h1><i class="fas fa-plus-circle"></i> নতুন পণ্য যোগ করুন</h1>
            <p>সরল ভার্সন - দ্রুত পণ্য যোগ করুন</p>
        </div>
        
        <div class="content">
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="category_id">ক্যাটাগরি নির্বাচন করুন *</label>
                    <select name="category_id" id="category_id" required>
                        <option value="">-- ক্যাটাগরি নির্বাচন করুন --</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="name">পণ্যের নাম *</label>
                    <input type="text" name="name" id="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="description">পণ্যের বিবরণ</label>
                    <textarea name="description" id="description" rows="4"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price">দাম (৳) *</label>
                        <input type="number" name="price" id="price" step="0.01" value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_quantity">স্টক পরিমাণ *</label>
                        <input type="number" name="stock_quantity" id="stock_quantity" value="<?php echo htmlspecialchars($_POST['stock_quantity'] ?? ''); ?>" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="image">পণ্যের ছবি</label>
                    <input type="file" name="image" id="image" accept="image/*">
                    <small style="color: #666; font-size: 14px;">সাপোর্টেড ফরমেট: JPG, JPEG, PNG, GIF, WebP</small>
                </div>
                
                <button type="submit" name="add_product" class="btn">
                    <i class="fas fa-plus"></i> পণ্য যোগ করুন
                </button>
            </form>
        </div>
    </div>
</body>
</html>
