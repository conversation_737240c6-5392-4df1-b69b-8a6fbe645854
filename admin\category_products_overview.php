<?php
require_once '../includes/error_handler.php';
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';
require_once '../includes/image_handler.php';

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get categories with product counts
$stmt = $pdo->query("
    SELECT c.*, COUNT(p.id) as product_count 
    FROM categories c 
    LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
    WHERE c.status = 'active'
    GROUP BY c.id 
    ORDER BY c.name
");
$categories = $stmt->fetchAll();

// Get selected category products
$selectedCategoryId = safe_get_param('category') ? intval(safe_get_param('category')) : null;
$products = [];
$selectedCategory = null;

if ($selectedCategoryId) {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ? AND status = 'active'");
    $stmt->execute([$selectedCategoryId]);
    $selectedCategory = $stmt->fetch();
    
    if ($selectedCategory) {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE category_id = ? AND status = 'active' ORDER BY name");
        $stmt->execute([$selectedCategoryId]);
        $products = $stmt->fetchAll();
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্যাটাগরি অনুসারে পণ্য দেখুন</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .nav-links {
            margin-top: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .main-content {
            padding: 30px;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .category-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }
        
        .category-card.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .category-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .category-card.active .category-icon {
            color: white;
        }
        
        .category-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .product-count {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .products-section {
            margin-top: 30px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .product-image {
            width: 100%;
            height: 150px;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .product-price {
            color: #667eea;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .product-stock {
            font-size: 14px;
            color: #666;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        /* Quick Edit Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-layer-group"></i> ক্যাটাগরি অনুসারে পণ্য</h1>
            <p>আপনার দোকানের সব পণ্য ক্যাটাগরি অনুসারে দেখুন</p>
            <div class="nav-links">
                <a href="dashboard.php"><i class="fas fa-home"></i> ড্যাশবোর্ড</a>
                <a href="add_product_by_category.php"><i class="fas fa-plus"></i> নতুন পণ্য যোগ</a>
                <a href="products.php"><i class="fas fa-list"></i> সব পণ্য</a>
            </div>
        </div>

        <div class="main-content">
            <!-- Categories Grid -->
            <div class="categories-grid">
                <?php foreach ($categories as $category): ?>
                    <div class="category-card <?php echo ($selectedCategoryId == $category['id']) ? 'active' : ''; ?>" 
                         onclick="window.location.href='?category=<?php echo $category['id']; ?>'">
                        <div class="category-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="category-name"><?php echo htmlspecialchars($category['name']); ?></div>
                        <div class="product-count"><?php echo $category['product_count']; ?> টি পণ্য</div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Products Section -->
            <?php if ($selectedCategory): ?>
                <div class="products-section">
                    <div class="section-header">
                        <h2><i class="fas fa-box"></i> <?php echo htmlspecialchars($selectedCategory['name']); ?> এর পণ্যসমূহ</h2>
                        <a href="add_product_by_category.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> নতুন পণ্য যোগ করুন
                        </a>
                    </div>

                    <?php if (empty($products)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-box-open"></i>
                            </div>
                            <h3>এই ক্যাটাগরিতে কোন পণ্য নেই</h3>
                            <p>এই ক্যাটাগরিতে প্রথম পণ্য যোগ করুন</p>
                            <a href="add_product_by_category.php" class="btn btn-primary" style="margin-top: 20px;">
                                <i class="fas fa-plus"></i> পণ্য যোগ করুন
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="products-grid">
                            <?php foreach ($products as $product): ?>
                                <div class="product-card">
                                    <div class="product-image">
                                        <?php echo ImageHandler::getImageHTML($product['image'], $product['name'], '', 'width: 100%; height: 100%; object-fit: cover; border-radius: 10px;', $product['id']); ?>
                                    </div>
                                    <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                                    <div class="product-price">৳<?php echo number_format($product['price'], 2); ?></div>
                                    <div class="product-stock">
                                        <?php if ($product['stock_quantity'] > 0): ?>
                                            <i class="fas fa-check-circle" style="color: #28a745;"></i> স্টকে আছে (<?php echo $product['stock_quantity']; ?>)
                                        <?php else: ?>
                                            <i class="fas fa-times-circle" style="color: #dc3545;"></i> স্টক শেষ
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-actions" style="margin-top: 15px; display: flex; gap: 8px; justify-content: center;">
                                        <a href="edit_product.php?id=<?php echo $product['id']; ?>" class="btn btn-primary" style="padding: 6px 12px; font-size: 12px; text-decoration: none;">
                                            <i class="fas fa-edit"></i> সম্পাদনা
                                        </a>
                                        <button onclick="quickEdit(<?php echo htmlspecialchars(json_encode($product)); ?>)" class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px; border: none; cursor: pointer;">
                                            <i class="fas fa-bolt"></i> দ্রুত
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h3>একটি ক্যাটাগরি নির্বাচন করুন</h3>
                    <p>উপরের ক্যাটাগরিগুলির মধ্যে যেকোনো একটিতে ক্লিক করুন</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Edit Modal -->
    <div id="quickEditModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-bolt"></i> দ্রুত সম্পাদনা</h3>
                <button class="modal-close" onclick="closeQuickEditModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="quickEditForm">
                    <input type="hidden" id="edit_product_id">

                    <div class="form-group">
                        <label for="edit_name">পণ্যের নাম</label>
                        <input type="text" id="edit_name" class="form-control" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_price">দাম (৳)</label>
                            <input type="number" id="edit_price" class="form-control" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="edit_stock">স্টক</label>
                            <input type="number" id="edit_stock" class="form-control" min="0" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_status">স্ট্যাটাস</label>
                        <select id="edit_status" class="form-control">
                            <option value="active">সক্রিয়</option>
                            <option value="inactive">নিষ্ক্রিয়</option>
                        </select>
                    </div>

                    <div class="form-group" style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                        <button type="button" class="btn btn-secondary" onclick="closeQuickEditModal()">
                            বাতিল
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> সংরক্ষণ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Add smooth scrolling to products section when category is selected
        if (window.location.search.includes('category=')) {
            setTimeout(() => {
                const productsSection = document.querySelector('.products-section');
                if (productsSection) {
                    productsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 100);
        }

        // Quick Edit Modal Functions
        function quickEdit(product) {
            document.getElementById('edit_product_id').value = product.id;
            document.getElementById('edit_name').value = product.name;
            document.getElementById('edit_price').value = product.price;
            document.getElementById('edit_stock').value = product.stock_quantity;
            document.getElementById('edit_status').value = product.status;

            document.getElementById('quickEditModal').style.display = 'block';
        }

        function closeQuickEditModal() {
            document.getElementById('quickEditModal').style.display = 'none';
        }

        // Handle quick edit form submission
        document.getElementById('quickEditForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const productId = document.getElementById('edit_product_id').value;
            const formData = new FormData();
            formData.append('action', 'quick_update');
            formData.append('product_id', productId);
            formData.append('name', document.getElementById('edit_name').value);
            formData.append('price', document.getElementById('edit_price').value);
            formData.append('stock_quantity', document.getElementById('edit_stock').value);
            formData.append('status', document.getElementById('edit_status').value);

            fetch('quick_edit_product.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('পণ্য সফলভাবে আপডেট করা হয়েছে!');
                    location.reload();
                } else {
                    alert('এরর: ' + data.message);
                }
            })
            .catch(error => {
                alert('আপডেট করতে সমস্যা হয়েছে।');
                console.error('Error:', error);
            });
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('quickEditModal');
            if (event.target === modal) {
                closeQuickEditModal();
            }
        }
    </script>
</body>
</html>
