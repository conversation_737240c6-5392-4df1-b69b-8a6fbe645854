-- Inventory Management System - Additional Tables
-- Run this after the main database_setup.sql

-- Buyers/Suppliers table
CREATE TABLE IF NOT EXISTS buyers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    company_name VA<PERSON><PERSON><PERSON>(255) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    email VARCHAR(255) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    buyer_type E<PERSON><PERSON>('supplier', 'wholesaler', 'retailer', 'individual') DEFAULT 'supplier',
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    current_balance DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT DEFAULT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Stock entries table for inventory tracking
CREATE TABLE IF NOT EXISTS stock_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    buyer_id INT DEFAULT NULL,
    entry_type ENUM('purchase', 'sale', 'adjustment') NOT NULL,
    quantity INT NOT NULL,
    purchase_price DECIMAL(10,2) DEFAULT NULL,
    selling_price DECIMAL(10,2) DEFAULT NULL,
    supplier_name VARCHAR(255) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    admin_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES buyers(id) ON DELETE SET NULL,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- Add status column to products table if not exists
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS status ENUM('draft', 'active', 'inactive') DEFAULT 'active' AFTER stock_quantity;

-- Add updated_at column to products table if not exists  
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_stock_entries_product ON stock_entries(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_entries_type ON stock_entries(entry_type);
CREATE INDEX IF NOT EXISTS idx_stock_entries_date ON stock_entries(created_at);

-- Sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Insert some draft products (without price/stock)
INSERT INTO products (name, description, category_id, sku, price, stock_quantity, status, created_at) VALUES 
('Samsung Galaxy A54', 'Samsung Galaxy A54 5G স্মার্টফোন', 1, 'DRAFT-1-001', 0, 0, 'draft', NOW()),
('iPhone 14', 'Apple iPhone 14 স্মার্টফোন', 1, 'DRAFT-1-002', 0, 0, 'draft', NOW()),
('পুরুষদের কটন শার্ট', 'উন্নত মানের কটন ফর্মাল শার্ট', 2, 'DRAFT-2-001', 0, 0, 'draft', NOW()),
('মহিলাদের কুর্তি', 'ঐতিহ্যবাহী বাংলাদেশী কুর্তি', 2, 'DRAFT-2-002', 0, 0, 'draft', NOW()),
('হুমায়ূন আহমেদের উপন্যাস', 'হিমু সিরিজের সংগ্রহ', 3, 'DRAFT-3-001', 0, 0, 'draft', NOW());
*/
