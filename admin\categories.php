<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_category') {
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        
        // Handle image upload
        $image_filename = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = upload_image($_FILES['image'], '../uploads/categories/');
            if ($upload_result['success']) {
                $image_filename = $upload_result['filename'];
            } else {
                $error = $upload_result['message'];
            }
        }
        
        if (!isset($error)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO categories (name, description, image) VALUES (?, ?, ?)");
                $stmt->execute([$name, $description, $image_filename]);
                
                log_activity('category_added', "Added category: $name", $_SESSION['admin_id'], 'admin');
                $success = 'ক্যাটেগরি সফলভাবে যোগ করা হয়েছে।';
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'edit_category') {
        $category_id = (int)$_POST['category_id'];
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        $status = sanitize_input($_POST['status']);
        
        // Handle image upload
        $image_filename = $_POST['existing_image'] ?? null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = upload_image($_FILES['image'], '../uploads/categories/');
            if ($upload_result['success']) {
                // Delete old image if exists
                if ($image_filename && file_exists('../uploads/categories/' . $image_filename)) {
                    unlink('../uploads/categories/' . $image_filename);
                }
                $image_filename = $upload_result['filename'];
            } else {
                $error = $upload_result['message'];
            }
        }
        
        if (!isset($error)) {
            try {
                $stmt = $pdo->prepare("UPDATE categories SET name = ?, description = ?, image = ?, status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$name, $description, $image_filename, $status, $category_id]);
                
                log_activity('category_updated', "Updated category: $name", $_SESSION['admin_id'], 'admin');
                $success = 'ক্যাটেগরি সফলভাবে আপডেট করা হয়েছে।';
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_category') {
        $category_id = (int)$_POST['category_id'];
        try {
            // Check if category has products
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ? AND status = 'active'");
            $stmt->execute([$category_id]);
            $productCount = $stmt->fetchColumn();
            
            if ($productCount > 0) {
                $error = 'এই ক্যাটেগরিতে পণ্য রয়েছে। প্রথমে পণ্যগুলো সরান বা অন্য ক্যাটেগরিতে স্থানান্তর করুন।';
            } else {
                $stmt = $pdo->prepare("UPDATE categories SET status = 'inactive' WHERE id = ?");
                $stmt->execute([$category_id]);
                
                log_activity('category_deleted', "Deleted category ID: $category_id", $_SESSION['admin_id'], 'admin');
                $success = 'ক্যাটেগরি সফলভাবে মুছে ফেলা হয়েছে।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}

// Get statistics
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $totalCategories = $stmt->fetchColumn();
} catch(PDOException $e) {
    $totalCategories = 0;
}

// Get categories
$categories = [];
try {
    $stmt = $pdo->prepare("SELECT c.*, 
                          (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as product_count 
                          FROM categories c 
                          WHERE c.status = 'active' 
                          ORDER BY c.created_at DESC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
}

// Create categories upload directory if it doesn't exist
if (!file_exists('../uploads/categories')) {
    mkdir('../uploads/categories', 0777, true);
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্যাটেগরি ব্যবস্থাপনা - অ্যাডমিন প্যানেল</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .category-image-thumb {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        .category-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .category-info {
            flex: 1;
        }
        .category-stats {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-size: 0.9rem;
            color: #666;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="add_product_by_category.php"><i class="fas fa-plus-circle"></i> ক্যাটাগরি অনুসারে পণ্য যোগ</a></li>
                <li><a href="categories.php" class="active"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="ssl_settings.php"><i class="fas fa-credit-card"></i> SSL Commerz</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>ক্যাটেগরি ব্যবস্থাপনা</h1>
                <button class="btn btn-primary" onclick="openAddCategoryModal()">
                    <i class="fas fa-plus"></i> নতুন ক্যাটেগরি যোগ করুন
                </button>
            </header>
            
            <div class="admin-body">
                <!-- Category Statistics -->
                <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
                        <i class="fas fa-tags" style="font-size: 2rem; color: #667eea; margin-bottom: 0.5rem;"></i>
                        <h3 style="margin: 0; font-size: 1.8rem; color: #333;"><?php echo $totalCategories; ?></h3>
                        <p style="margin: 0; color: #666;">মোট ক্যাটেগরি</p>
                    </div>
                    <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
                        <i class="fas fa-eye" style="font-size: 2rem; color: #2ed573; margin-bottom: 0.5rem;"></i>
                        <h3 style="margin: 0; font-size: 1.8rem; color: #333;">
                            <?php
                            $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE status = 'active'");
                            echo $stmt->fetchColumn();
                            ?>
                        </h3>
                        <p style="margin: 0; color: #666;">সক্রিয় ক্যাটেগরি</p>
                    </div>
                    <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
                        <i class="fas fa-box" style="font-size: 2rem; color: #ffa502; margin-bottom: 0.5rem;"></i>
                        <h3 style="margin: 0; font-size: 1.8rem; color: #333;">
                            <?php
                            $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
                            echo $stmt->fetchColumn();
                            ?>
                        </h3>
                        <p style="margin: 0; color: #666;">মোট পণ্য</p>
                    </div>
                    <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
                        <i class="fas fa-layer-group" style="font-size: 2rem; color: #ff4757; margin-bottom: 0.5rem;"></i>
                        <h3 style="margin: 0; font-size: 1.8rem; color: #333;">
                            <?php
                            $stmt = $pdo->query("SELECT AVG(product_count) FROM (SELECT COUNT(*) as product_count FROM products WHERE status = 'active' GROUP BY category_id) as avg_products");
                            echo round($stmt->fetchColumn() ?: 0);
                            ?>
                        </h3>
                        <p style="margin: 0; color: #666;">গড় পণ্য/ক্যাটেগরি</p>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Categories List -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-list"></i> ক্যাটেগরির তালিকা
                    </div>
                    <div class="card-body">
                        <?php if (empty($categories)): ?>
                            <p>কোন ক্যাটেগরি পাওয়া যায়নি।</p>
                        <?php else: ?>
                            <?php foreach ($categories as $category): ?>
                                <div class="category-card">
                                    <img src="<?php echo $category['image'] ? '../uploads/categories/' . $category['image'] : '../assets/images/no-image.svg'; ?>" 
                                         alt="<?php echo $category['name']; ?>" class="category-image-thumb">
                                    <div class="category-info">
                                        <h4><?php echo $category['name']; ?></h4>
                                        <p><?php echo $category['description'] ?: 'কোন বিবরণ নেই'; ?></p>
                                        <div class="category-stats">
                                            <i class="fas fa-box"></i> <?php echo $category['product_count']; ?>টি পণ্য
                                        </div>
                                    </div>
                                    <div class="actions">
                                        <button class="btn btn-secondary btn-sm" onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)">
                                            <i class="fas fa-edit"></i> সম্পাদনা
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo addslashes($category['name']); ?>')">
                                            <i class="fas fa-trash"></i> মুছুন
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Add Category Modal -->
    <div id="addCategoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>নতুন ক্যাটেগরি যোগ করুন</h3>
                <button class="modal-close" onclick="closeAddCategoryModal()">&times;</button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="addCategoryForm">
                <input type="hidden" name="action" value="add_category">
                
                <div class="form-group">
                    <label for="name">ক্যাটেগরির নাম *</label>
                    <input type="text" name="name" id="name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="description">বিবরণ</label>
                    <textarea name="description" id="description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="image">ক্যাটেগরির ছবি</label>
                    <input type="file" name="image" id="image" class="form-control" accept="image/*">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeAddCategoryModal()">
                        বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>ক্যাটেগরি সম্পাদনা করুন</h3>
                <button class="modal-close" onclick="closeEditCategoryModal()">&times;</button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="editCategoryForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="category_id" id="edit_category_id">
                <input type="hidden" name="existing_image" id="edit_existing_image">
                
                <div class="form-group">
                    <label for="edit_name">ক্যাটেগরির নাম *</label>
                    <input type="text" name="name" id="edit_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_description">বিবরণ</label>
                    <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit_status">স্ট্যাটাস</label>
                    <select name="status" id="edit_status" class="form-control">
                        <option value="active">সক্রিয়</option>
                        <option value="inactive">নিষ্ক্রিয়</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_image">ক্যাটেগরির ছবি</label>
                    <input type="file" name="image" id="edit_image" class="form-control" accept="image/*">
                    <small>নতুন ছবি আপলোড করলে পুরানো ছবি প্রতিস্থাপিত হবে।</small>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> আপডেট করুন
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeEditCategoryModal()">
                        বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="../assets/js/jquery.min.js"></script>
    <script>
        function openAddCategoryModal() {
            $('#addCategoryModal').show();
        }
        
        function closeAddCategoryModal() {
            $('#addCategoryModal').hide();
            $('#addCategoryForm')[0].reset();
        }
        
        function openEditCategoryModal() {
            $('#editCategoryModal').show();
        }
        
        function closeEditCategoryModal() {
            $('#editCategoryModal').hide();
            $('#editCategoryForm')[0].reset();
        }
        
        function editCategory(category) {
            $('#edit_category_id').val(category.id);
            $('#edit_name').val(category.name);
            $('#edit_description').val(category.description || '');
            $('#edit_status').val(category.status);
            $('#edit_existing_image').val(category.image || '');
            openEditCategoryModal();
        }
        
        function deleteCategory(id, name) {
            if (confirm('আপনি কি নিশ্চিত যে "' + name + '" ক্যাটেগরিটি মুছে ফেলতে চান?')) {
                const form = $('<form method="POST">' +
                    '<input type="hidden" name="action" value="delete_category">' +
                    '<input type="hidden" name="category_id" value="' + id + '">' +
                    '</form>');
                $('body').append(form);
                form.submit();
            }
        }
        
        // Close modal when clicking outside
        $(window).click(function(event) {
            if (event.target.id === 'addCategoryModal') {
                closeAddCategoryModal();
            }
            if (event.target.id === 'editCategoryModal') {
                closeEditCategoryModal();
            }
        });
    </script>
</body>
</html>
