<?php
header('Content-Type: application/json');

require_once '../config/session.php';
require_once '../config/database.php';
require_once '../includes/sku_generator.php';

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত অ্যাক্সেস']);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    $categoryId = intval($_POST['category_id'] ?? 0);
    $productName = trim($_POST['product_name'] ?? '');
    
    if (!$categoryId) {
        throw new Exception('ক্যাটাগরি ID আবশ্যক');
    }
    
    if (empty($productName)) {
        throw new Exception('পণ্যের নাম আবশ্যক');
    }
    
    // Generate SKU options
    $skuGenerator = new SKUGenerator($pdo);
    $options = $skuGenerator->generateSKUOptions($categoryId, $productName);
    
    echo json_encode([
        'success' => true,
        'options' => $options,
        'message' => 'SKU অপশন সফলভাবে জেনারেট হয়েছে'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
