<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Get dashboard statistics
try {
    // Total products
    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $totalProducts = $stmt->fetchColumn();
    
    // Total categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE status = 'active'");
    $totalCategories = $stmt->fetchColumn();
    
    // Total customers
    $stmt = $pdo->query("SELECT COUNT(*) FROM customers WHERE status = 'active'");
    $totalCustomers = $stmt->fetchColumn();
    
    // Total orders
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
    $totalOrders = $stmt->fetchColumn();
    
    // Recent orders
    $stmt = $pdo->prepare("SELECT o.*, c.name as customer_name FROM orders o 
                          LEFT JOIN customers c ON o.customer_id = c.id 
                          ORDER BY o.created_at DESC LIMIT 5");
    $stmt->execute();
    $recentOrders = $stmt->fetchAll();
    
    // Low stock products
    $stmt = $pdo->prepare("SELECT * FROM products WHERE stock_quantity <= 10 AND status = 'active' ORDER BY stock_quantity ASC LIMIT 5");
    $stmt->execute();
    $lowStockProducts = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্যাশবোর্ড - অ্যাডমিন প্যানেল</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-weight: 500;
        }
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }
        .card-body {
            padding: 1.5rem;
        }
        .quick-actions {
            margin: 2rem 0;
        }

        .quick-actions h3 {
            margin-bottom: 1rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            display: block;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            color: white;
        }

        .action-card i {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
        }

        .action-card h4 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .action-card p {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="add_product_by_category.php"><i class="fas fa-plus-circle"></i> নতুন পণ্য যোগ</a></li>
                <li><a href="category_products_overview.php"><i class="fas fa-layer-group"></i> ক্যাটাগরি অনুসারে দেখুন</a></li>
                <li><a href="upload_product_image.php"><i class="fas fa-images"></i> পণ্যের ছবি আপলোড</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="salesmen.php"><i class="fas fa-user-tie"></i> সেলসম্যান</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="ssl_settings.php"><i class="fas fa-credit-card"></i> SSL Commerz</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>ড্যাশবোর্ড</h1>
                <div>
                    <span>আজকের তারিখ: <?php echo date('d/m/Y'); ?></span>
                </div>
            </header>
            
            <div class="admin-body">
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <i class="fas fa-box" style="color: #667eea;"></i>
                        <h3><?php echo $totalProducts; ?></h3>
                        <p>মোট পণ্য</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-tags" style="color: #2ed573;"></i>
                        <h3><?php echo $totalCategories; ?></h3>
                        <p>ক্যাটেগরি</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users" style="color: #ffa502;"></i>
                        <h3><?php echo $totalCustomers; ?></h3>
                        <p>কাস্টমার</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart" style="color: #ff4757;"></i>
                        <h3><?php echo $totalOrders; ?></h3>
                        <p>মোট অর্ডার</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3><i class="fas fa-bolt"></i> দ্রুত কাজ</h3>
                    <div class="actions-grid">
                        <a href="inventory_management.php" class="action-card">
                            <i class="fas fa-warehouse"></i>
                            <h4>ইনভেন্টরি ম্যানেজমেন্ট</h4>
                            <p>পণ্যের নাম যোগ → স্টক ও দাম যোগ</p>
                        </a>
                        <a href="products.php" class="action-card">
                            <i class="fas fa-box"></i>
                            <h4>পণ্য ব্যবস্থাপনা</h4>
                            <p>পণ্য দেখুন ও সম্পাদনা করুন</p>
                        </a>
                        <a href="add_product_by_category.php" class="action-card">
                            <i class="fas fa-plus-circle"></i>
                            <h4>ক্যাটাগরি অনুসারে পণ্য</h4>
                            <p>ক্যাটাগরি নির্বাচন করে পণ্য যোগ</p>
                        </a>
                        <a href="categories.php" class="action-card">
                            <i class="fas fa-tags"></i>
                            <h4>ক্যাটাগরি ব্যবস্থাপনা</h4>
                            <p>নতুন ক্যাটাগরি যোগ ও সম্পাদনা</p>
                        </a>
                    </div>
                </div>

                <!-- Dashboard Content Grid -->
                <div class="dashboard-grid">
                    <!-- Recent Orders -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-clock"></i> সাম্প্রতিক অর্ডার
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentOrders)): ?>
                                <p>কোন অর্ডার পাওয়া যায়নি।</p>
                            <?php else: ?>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>অর্ডার নং</th>
                                            <th>কাস্টমার</th>
                                            <th>মোট</th>
                                            <th>স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td><?php echo $order['order_number']; ?></td>
                                            <td><?php echo $order['customer_name']; ?></td>
                                            <td><?php echo format_price($order['total_amount']); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $order['status']; ?>">
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Low Stock Products -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-exclamation-triangle"></i> কম স্টক পণ্য
                        </div>
                        <div class="card-body">
                            <?php if (empty($lowStockProducts)): ?>
                                <p>সব পণ্যের স্টক পর্যাপ্ত।</p>
                            <?php else: ?>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>পণ্যের নাম</th>
                                            <th>স্টক</th>
                                            <th>দাম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($lowStockProducts as $product): ?>
                                        <tr>
                                            <td><?php echo $product['name']; ?></td>
                                            <td>
                                                <span class="badge badge-warning">
                                                    <?php echo $product['stock_quantity']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo format_price($product['price']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
