<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Preview Test</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            padding: 2rem;
            background: #f5f5f5;
            font-family: 'Hind Siliguri', sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        .test-image-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .test-image-card:hover {
            transform: translateY(-5px);
        }
        .debug-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🖼️ Image Preview Test</h1>
        
        <div class="debug-info">
            <h3>Debug Information:</h3>
            <p id="jqueryStatus">jQuery Status: Checking...</p>
            <p id="functionStatus">showImagePreview Function: Checking...</p>
            <p id="cssStatus">CSS Loaded: Checking...</p>
        </div>
        
        <h3>Test Images (Click to Preview):</h3>
        <div class="test-images">
            <div class="test-image-card">
                <div class="product-image-container">
                    <img src="assets/images/demo-product.svg"
                         alt="Demo SVG Product"
                         class="product-image"
                         onclick="showImagePreview('assets/images/demo-product.svg', 'Demo SVG Product')"
                         style="cursor: pointer;">
                </div>
                <div style="padding: 1rem;">
                    <h4>SVG Test Image</h4>
                    <p>Click to test SVG preview</p>
                </div>
            </div>
            
            <div class="test-image-card">
                <div class="product-image-container">
                    <img src="assets/images/no-image.svg"
                         alt="No Image Placeholder"
                         class="product-image"
                         onclick="showImagePreview('assets/images/no-image.svg', 'No Image Placeholder')"
                         style="cursor: pointer;">
                </div>
                <div style="padding: 1rem;">
                    <h4>No Image SVG</h4>
                    <p>Click to test placeholder</p>
                </div>
            </div>
            
            <div class="test-image-card">
                <div class="product-image-container">
                    <img src="uploads/products/resized_product_fan_1752816333_2574.png"
                         alt="Fan Product"
                         class="product-image"
                         onclick="showImagePreview('uploads/products/resized_product_fan_1752816333_2574.png', 'Fan Product')"
                         style="cursor: pointer;">
                </div>
                <div style="padding: 1rem;">
                    <h4>Real Product Image</h4>
                    <p>Click to test real image</p>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 2rem;">
            <button onclick="manualTest()" class="btn btn-primary">Manual Test Button</button>
            <button onclick="checkFunctions()" class="btn btn-secondary">Check Functions</button>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function testImagePreview(src, name) {
            console.log('testImagePreview called with:', src, name);
            
            if (typeof showImagePreview === 'function') {
                console.log('showImagePreview function exists, calling it...');
                showImagePreview(src, name);
            } else {
                console.error('showImagePreview function not found!');
                alert('showImagePreview function not found!');
            }
        }
        
        function manualTest() {
            console.log('Manual test started');
            testImagePreview('assets/images/demo-product.svg', 'Manual Test Image');
        }
        
        function checkFunctions() {
            console.log('=== Function Check ===');
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('showImagePreview exists:', typeof showImagePreview === 'function');
            console.log('closeImagePreview exists:', typeof closeImagePreview === 'function');
            
            // Check if modal exists
            console.log('Modal exists:', $('#imagePreviewModal').length > 0);
            
            // Check CSS
            const testElement = $('<div class="image-preview-modal"></div>');
            $('body').append(testElement);
            const display = testElement.css('display');
            testElement.remove();
            console.log('CSS loaded (modal display):', display);
        }
        
        $(document).ready(function() {
            // Update debug info
            $('#jqueryStatus').text('jQuery Status: ' + (typeof $ !== 'undefined' ? '✅ Loaded' : '❌ Not Loaded'));
            $('#functionStatus').text('showImagePreview Function: ' + (typeof showImagePreview === 'function' ? '✅ Available' : '❌ Not Available'));
            
            // Check CSS
            const testElement = $('<div class="image-preview-modal"></div>');
            $('body').append(testElement);
            const cssLoaded = testElement.css('position') === 'fixed';
            testElement.remove();
            $('#cssStatus').text('CSS Loaded: ' + (cssLoaded ? '✅ Yes' : '❌ No'));
            
            console.log('Test page loaded');
            checkFunctions();
        });
    </script>
</body>
</html>
