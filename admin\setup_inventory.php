<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_tables'])) {
    try {
        // Create buyers table
        $sql = "CREATE TABLE IF NOT EXISTS buyers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            company_name VARCHAR(255) DEFAULT NULL,
            phone VARCHAR(20) DEFAULT NULL,
            email VARCHAR(255) DEFAULT NULL,
            address TEXT DEFAULT NULL,
            buyer_type ENUM('supplier', 'wholesaler', 'retailer', 'individual') DEFAULT 'supplier',
            credit_limit DECIMAL(10,2) DEFAULT 0.00,
            current_balance DECIMAL(10,2) DEFAULT 0.00,
            notes TEXT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        
        // Update stock_entries table to add buyer_id
        $sql = "CREATE TABLE IF NOT EXISTS stock_entries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            buyer_id INT DEFAULT NULL,
            entry_type ENUM('purchase', 'sale', 'adjustment') NOT NULL,
            quantity INT NOT NULL,
            purchase_price DECIMAL(10,2) DEFAULT NULL,
            selling_price DECIMAL(10,2) DEFAULT NULL,
            supplier_name VARCHAR(255) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            admin_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (buyer_id) REFERENCES buyers(id) ON DELETE SET NULL,
            FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE
        )";
        
        $pdo->exec($sql);
        
        // Add status column to products table if not exists
        try {
            $pdo->exec("ALTER TABLE products ADD COLUMN status ENUM('draft', 'active', 'inactive') DEFAULT 'active' AFTER stock_quantity");
        } catch (PDOException $e) {
            // Column might already exist, ignore error
        }
        
        // Add updated_at column to products table if not exists  
        try {
            $pdo->exec("ALTER TABLE products ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        } catch (PDOException $e) {
            // Column might already exist, ignore error
        }
        
        // Create indexes for better performance
        try {
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_products_status ON products(status)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_stock_entries_product ON stock_entries(product_id)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_stock_entries_type ON stock_entries(entry_type)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_stock_entries_date ON stock_entries(created_at)");
        } catch (PDOException $e) {
            // Indexes might already exist, ignore error
        }
        
        // Insert sample buyers
        $sampleBuyers = [
            ['আহমেদ ভাই', 'ABC ট্রেডিং', '01712345678', '<EMAIL>', 'ঢাকা, বাংলাদেশ', 'supplier', 50000],
            ['রহিম সাহেব', 'XYZ হোলসেল', '01812345678', '<EMAIL>', 'চট্টগ্রাম, বাংলাদেশ', 'wholesaler', 30000],
            ['করিম ভাই', '', '01912345678', '', 'সিলেট, বাংলাদেশ', 'individual', 10000]
        ];
        
        foreach ($sampleBuyers as $buyer) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO buyers (name, company_name, phone, email, address, buyer_type, credit_limit) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($buyer);
        }
        
        $message = 'ইনভেন্টরি সিস্টেম সফলভাবে সেটআপ করা হয়েছে! এখন ইনভেন্টরি ম্যানেজমেন্ট ব্যবহার করতে পারেন।';
        $messageType = 'success';
        
    } catch (PDOException $e) {
        $message = 'সেটআপ এরর: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Check if tables exist
$tablesExist = false;
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'buyers'");
    $tablesExist = $stmt->rowCount() > 0;
} catch (PDOException $e) {
    // Ignore error
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ইনভেন্টরি সিস্টেম সেটআপ</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 40px;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-card.ready {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.not-ready {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            width: 100%;
            justify-content: center;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list i {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> ইনভেন্টরি সিস্টেম সেটআপ</h1>
            <p>আপনার ইনভেন্টরি ম্যানেজমেন্ট সিস্টেম প্রস্তুত করুন</p>
        </div>
        
        <div class="content">
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($tablesExist): ?>
                <div class="status-card ready">
                    <i class="fas fa-check-circle fa-3x" style="color: #28a745; margin-bottom: 20px;"></i>
                    <h2>সিস্টেম প্রস্তুত!</h2>
                    <p>ইনভেন্টরি ম্যানেজমেন্ট সিস্টেম ব্যবহারের জন্য প্রস্তুত।</p>
                    
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> বায়ার ম্যানেজমেন্ট</li>
                        <li><i class="fas fa-check"></i> স্টক ট্র্যাকিং</li>
                        <li><i class="fas fa-check"></i> পণ্য ব্যবস্থাপনা</li>
                        <li><i class="fas fa-check"></i> ইনভেন্টরি রিপোর্ট</li>
                    </ul>
                    
                    <a href="inventory_management.php" class="btn btn-success">
                        <i class="fas fa-warehouse"></i> ইনভেন্টরি ম্যানেজমেন্ট শুরু করুন
                    </a>
                </div>
            <?php else: ?>
                <div class="status-card not-ready">
                    <i class="fas fa-exclamation-triangle fa-3x" style="color: #ffc107; margin-bottom: 20px;"></i>
                    <h2>সেটআপ প্রয়োজন</h2>
                    <p>ইনভেন্টরি ম্যানেজমেন্ট ব্যবহার করার আগে প্রয়োজনীয় টেবিল তৈরি করুন।</p>
                    
                    <ul class="feature-list">
                        <li><i class="fas fa-plus"></i> বায়ার টেবিল তৈরি</li>
                        <li><i class="fas fa-plus"></i> স্টক এন্ট্রি টেবিল আপডেট</li>
                        <li><i class="fas fa-plus"></i> পণ্য টেবিল আপডেট</li>
                        <li><i class="fas fa-plus"></i> নমুনা ডেটা যোগ</li>
                    </ul>
                    
                    <form method="POST">
                        <button type="submit" name="setup_tables" class="btn">
                            <i class="fas fa-magic"></i> এখনই সেটআপ করুন
                        </button>
                    </form>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="dashboard.php" style="color: #667eea; text-decoration: none;">
                    <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরুন
                </a>
            </div>
        </div>
    </div>
</body>
</html>
