<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';
require_once '../config/functions.php';

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['add_product_name'])) {
            // Add product name only (without price/stock)
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category_id = intval($_POST['category_id'] ?? 0);
            
            if (empty($name) || empty($category_id)) {
                throw new Exception('পণ্যের নাম এবং ক্যাটাগরি আবশ্যক।');
            }
            
            // Check if product already exists
            $stmt = $pdo->prepare("SELECT id FROM products WHERE name = ? AND category_id = ?");
            $stmt->execute([$name, $category_id]);
            if ($stmt->fetch()) {
                throw new Exception('এই নামের পণ্য ইতিমধ্যে এই ক্যাটাগরিতে আছে।');
            }
            
            // Generate SKU
            $sku = 'PROD-' . $category_id . '-' . time();
            
            // Insert product with default values
            $stmt = $pdo->prepare("INSERT INTO products (name, description, category_id, sku, price, stock_quantity, status, created_at) VALUES (?, ?, ?, ?, 0, 0, 'draft', NOW())");
            $result = $stmt->execute([$name, $description, $category_id, $sku]);
            
            if ($result) {
                $message = 'পণ্যের নাম সফলভাবে যোগ করা হয়েছে! এখন স্টক ও দাম যোগ করুন।';
                $messageType = 'success';
            }
            
        } elseif (isset($_POST['add_stock'])) {
            // Add stock and price to existing product
            $product_id = intval($_POST['product_id'] ?? 0);
            $purchase_price = floatval($_POST['purchase_price'] ?? 0);
            $selling_price = floatval($_POST['selling_price'] ?? 0);
            $new_stock = intval($_POST['new_stock'] ?? 0);
            $supplier_name = trim($_POST['supplier_name'] ?? '');
            
            if (empty($product_id) || empty($purchase_price) || empty($selling_price) || empty($new_stock)) {
                throw new Exception('সব ক্ষেত্র পূরণ করুন।');
            }
            
            // Get current stock
            $stmt = $pdo->prepare("SELECT stock_quantity, price FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if (!$product) {
                throw new Exception('পণ্য পাওয়া যায়নি।');
            }
            
            $current_stock = $product['stock_quantity'];
            $total_stock = $current_stock + $new_stock;
            
            // Handle image upload
            $image_filename = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/products/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (in_array($file_extension, $allowed_extensions)) {
                    $image_filename = 'product_' . $product_id . '_' . time() . '.' . $file_extension;
                    $target_path = $upload_dir . $image_filename;
                    
                    if (!move_uploaded_file($_FILES['image']['tmp_name'], $target_path)) {
                        throw new Exception('ছবি আপলোড করতে সমস্যা হয়েছে।');
                    }
                }
            }
            
            // Update product with new stock and price
            if ($image_filename) {
                $stmt = $pdo->prepare("UPDATE products SET price = ?, stock_quantity = ?, image = ?, status = 'active', updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$selling_price, $total_stock, $image_filename, $product_id]);
            } else {
                $stmt = $pdo->prepare("UPDATE products SET price = ?, stock_quantity = ?, status = 'active', updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$selling_price, $total_stock, $product_id]);
            }
            
            // Record stock entry
            $stmt = $pdo->prepare("INSERT INTO stock_entries (product_id, entry_type, quantity, purchase_price, selling_price, supplier_name, admin_id, created_at) VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$product_id, $new_stock, $purchase_price, $selling_price, $supplier_name, $_SESSION['admin_id']]);
            
            if ($result) {
                $message = "স্টক সফলভাবে যোগ করা হয়েছে! পূর্বের স্টক: {$current_stock}, নতুন স্টক: {$new_stock}, মোট স্টক: {$total_stock}";
                $messageType = 'success';
            }
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get categories
$stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
$categories = $stmt->fetchAll();

// Get products without stock/price (draft status)
$stmt = $pdo->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'draft' ORDER BY p.created_at DESC");
$draft_products = $stmt->fetchAll();

// Get products with stock (active status)
$stmt = $pdo->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'active' ORDER BY p.updated_at DESC LIMIT 20");
$active_products = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ইনভেন্টরি ম্যানেজমেন্ট | দোকান</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .tab {
            padding: 15px 30px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .product-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .product-card.draft {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .product-card.active {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .product-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .status-draft {
            background: #ffc107;
            color: #856404;
        }
        
        .status-active {
            background: #28a745;
            color: white;
        }
        
        .stock-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .stock-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .back-btn {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="dashboard.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> ড্যাশবোর্ড
            </a>
            <h1><i class="fas fa-warehouse"></i> ইনভেন্টরি ম্যানেজমেন্ট</h1>
            <p>পণ্যের নাম তৈরি করুন → স্টক ও দাম যোগ করুন → বিক্রয় করুন</p>
        </div>
        
        <div class="content">
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('add-product')">
                    <i class="fas fa-plus"></i> পণ্যের নাম যোগ করুন
                </button>
                <button class="tab" onclick="showTab('add-stock')">
                    <i class="fas fa-boxes"></i> স্টক ও দাম যোগ করুন
                </button>
                <button class="tab" onclick="showTab('view-products')">
                    <i class="fas fa-list"></i> পণ্য তালিকা
                </button>
            </div>
            
            <!-- Tab 1: Add Product Name -->
            <div id="add-product" class="tab-content active">
                <div class="form-section">
                    <h2><i class="fas fa-tag"></i> নতুন পণ্যের নাম যোগ করুন</h2>
                    <p style="color: #666; margin-bottom: 20px;">প্রথমে পণ্যের নাম ও ক্যাটাগরি নির্ধারণ করুন। পরে স্টক ও দাম যোগ করবেন।</p>
                    
                    <form method="POST">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="category_id">ক্যাটাগরি নির্বাচন করুন *</label>
                                <select name="category_id" id="category_id" required>
                                    <option value="">-- ক্যাটাগরি নির্বাচন করুন --</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="name">পণ্যের নাম *</label>
                                <input type="text" name="name" id="name" placeholder="যেমন: Samsung Galaxy A54" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">পণ্যের বিবরণ (ঐচ্ছিক)</label>
                            <textarea name="description" id="description" rows="3" placeholder="পণ্যের বিস্তারিত বিবরণ লিখুন..."></textarea>
                        </div>
                        
                        <button type="submit" name="add_product_name" class="btn">
                            <i class="fas fa-plus"></i> পণ্যের নাম যোগ করুন
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Tab 2: Add Stock -->
            <div id="add-stock" class="tab-content">
                <div class="form-section">
                    <h2><i class="fas fa-boxes"></i> স্টক ও দাম যোগ করুন</h2>
                    <p style="color: #666; margin-bottom: 20px;">বায়ার থেকে পণ্য কিনলে এখানে স্টক ও দাম যোগ করুন।</p>
                    
                    <?php if (empty($draft_products)): ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-box-open fa-3x" style="margin-bottom: 20px; opacity: 0.5;"></i>
                            <h3>কোন পণ্য পাওয়া যায়নি</h3>
                            <p>প্রথমে "পণ্যের নাম যোগ করুন" ট্যাব থেকে পণ্যের নাম যোগ করুন।</p>
                        </div>
                    <?php else: ?>
                        <form method="POST" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="product_id">পণ্য নির্বাচন করুন *</label>
                                <select name="product_id" id="product_id" required onchange="showCurrentStock(this.value)">
                                    <option value="">-- পণ্য নির্বাচন করুন --</option>
                                    <?php foreach ($draft_products as $product): ?>
                                        <option value="<?php echo $product['id']; ?>" data-stock="<?php echo $product['stock_quantity']; ?>">
                                            <?php echo htmlspecialchars($product['name']); ?> (<?php echo htmlspecialchars($product['category_name']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div id="current-stock-info" style="display: none;" class="stock-info">
                                <h4>বর্তমান স্টক তথ্য:</h4>
                                <div class="stock-row">
                                    <span>পূর্বের স্টক:</span>
                                    <span id="current-stock">0</span>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="purchase_price">ক্রয় মূল্য (৳) *</label>
                                    <input type="number" name="purchase_price" id="purchase_price" step="0.01" placeholder="যে দামে কিনেছেন" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="selling_price">বিক্রয় মূল্য (৳) *</label>
                                    <input type="number" name="selling_price" id="selling_price" step="0.01" placeholder="যে দামে বিক্রি করবেন" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new_stock">নতুন স্টক সংখ্যা *</label>
                                    <input type="number" name="new_stock" id="new_stock" placeholder="কতটি পণ্য কিনেছেন" required onchange="calculateTotalStock()">
                                </div>
                                
                                <div class="form-group">
                                    <label for="supplier_name">সরবরাহকারীর নাম</label>
                                    <input type="text" name="supplier_name" id="supplier_name" placeholder="কার কাছ থেকে কিনেছেন">
                                </div>
                            </div>
                            
                            <div id="total-stock-info" style="display: none;" class="stock-info">
                                <h4>স্টক হিসাব:</h4>
                                <div class="stock-row">
                                    <span>পূর্বের স্টক:</span>
                                    <span id="prev-stock-display">0</span>
                                </div>
                                <div class="stock-row">
                                    <span>নতুন স্টক:</span>
                                    <span id="new-stock-display">0</span>
                                </div>
                                <div class="stock-row" style="border-top: 2px solid #667eea; padding-top: 10px; margin-top: 10px; font-weight: bold;">
                                    <span>মোট স্টক:</span>
                                    <span id="total-stock-display">0</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="image">পণ্যের ছবি</label>
                                <input type="file" name="image" id="image" accept="image/*">
                                <small style="color: #666;">সাপোর্টেড ফরমেট: JPG, JPEG, PNG, GIF, WebP</small>
                            </div>
                            
                            <button type="submit" name="add_stock" class="btn btn-success">
                                <i class="fas fa-plus"></i> স্টক ও দাম যোগ করুন
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Tab 3: View Products -->
            <div id="view-products" class="tab-content">
                <h2><i class="fas fa-list"></i> পণ্য তালিকা</h2>
                
                <?php if (!empty($draft_products)): ?>
                    <h3 style="color: #ffc107; margin: 20px 0;">অসম্পূর্ণ পণ্য (স্টক ও দাম যোগ করুন)</h3>
                    <div class="products-grid">
                        <?php foreach ($draft_products as $product): ?>
                            <div class="product-card draft">
                                <span class="product-status status-draft">অসম্পূর্ণ</span>
                                <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                                <p><strong>ক্যাটাগরি:</strong> <?php echo htmlspecialchars($product['category_name']); ?></p>
                                <p><strong>SKU:</strong> <?php echo htmlspecialchars($product['sku']); ?></p>
                                <?php if ($product['description']): ?>
                                    <p><strong>বিবরণ:</strong> <?php echo htmlspecialchars($product['description']); ?></p>
                                <?php endif; ?>
                                <p style="color: #856404; font-weight: bold; margin-top: 10px;">
                                    <i class="fas fa-exclamation-triangle"></i> স্টক ও দাম যোগ করুন
                                </p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($active_products)): ?>
                    <h3 style="color: #28a745; margin: 30px 0 20px 0;">সক্রিয় পণ্য (বিক্রয়ের জন্য প্রস্তুত)</h3>
                    <div class="products-grid">
                        <?php foreach ($active_products as $product): ?>
                            <div class="product-card active">
                                <span class="product-status status-active">সক্রিয়</span>
                                <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                                <p><strong>ক্যাটাগরি:</strong> <?php echo htmlspecialchars($product['category_name']); ?></p>
                                <p><strong>SKU:</strong> <?php echo htmlspecialchars($product['sku']); ?></p>
                                
                                <div class="stock-info">
                                    <div class="stock-row">
                                        <span>বিক্রয় মূল্য:</span>
                                        <span style="font-weight: bold; color: #28a745;">৳<?php echo number_format($product['price'], 2); ?></span>
                                    </div>
                                    <div class="stock-row">
                                        <span>মোট স্টক:</span>
                                        <span style="font-weight: bold; color: #667eea;"><?php echo $product['stock_quantity']; ?>টি</span>
                                    </div>
                                </div>
                                
                                <?php if ($product['image']): ?>
                                    <img src="../uploads/products/<?php echo htmlspecialchars($product['image']); ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                         style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-top: 10px;">
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($draft_products) && empty($active_products)): ?>
                    <div style="text-align: center; padding: 60px; color: #666;">
                        <i class="fas fa-box-open fa-4x" style="margin-bottom: 20px; opacity: 0.3;"></i>
                        <h3>কোন পণ্য পাওয়া যায়নি</h3>
                        <p>প্রথমে "পণ্যের নাম যোগ করুন" ট্যাব থেকে শুরু করুন।</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        function showCurrentStock(productId) {
            const select = document.getElementById('product_id');
            const option = select.querySelector(`option[value="${productId}"]`);
            const currentStock = option ? option.dataset.stock : 0;
            
            document.getElementById('current-stock').textContent = currentStock;
            document.getElementById('current-stock-info').style.display = productId ? 'block' : 'none';
            
            calculateTotalStock();
        }
        
        function calculateTotalStock() {
            const currentStock = parseInt(document.getElementById('current-stock').textContent) || 0;
            const newStock = parseInt(document.getElementById('new_stock').value) || 0;
            const totalStock = currentStock + newStock;
            
            document.getElementById('prev-stock-display').textContent = currentStock;
            document.getElementById('new-stock-display').textContent = newStock;
            document.getElementById('total-stock-display').textContent = totalStock;
            
            document.getElementById('total-stock-info').style.display = newStock > 0 ? 'block' : 'none';
        }
    </script>
</body>
</html>
