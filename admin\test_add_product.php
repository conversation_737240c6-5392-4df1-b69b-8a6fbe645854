<?php
// Simple test page
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    echo "Admin not logged in. <a href='login.php'>Login here</a>";
    exit;
}

require_once '../config/database.php';

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
    exit;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Product</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .category-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .category-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-card:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-5px);
        }
        
        .category-card.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }
        
        .form-section.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Hind Siliguri', sans-serif;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .debug-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-plus-circle"></i> ক্যাটাগরি অনুসারে পণ্য যোগ করুন (টেস্ট)</h1>
            <p>এটি একটি সরলীকৃত টেস্ট ভার্সন</p>
        </div>
        
        <div class="content">
            <div class="debug-info">
                <h3><i class="fas fa-info-circle"></i> ডিবাগ তথ্য:</h3>
                <p><strong>Admin ID:</strong> <?php echo $_SESSION['admin_id'] ?? 'Not set'; ?></p>
                <p><strong>Categories found:</strong> <?php echo count($categories); ?></p>
                <p><strong>Database connection:</strong> <?php echo $pdo ? 'Connected' : 'Not connected'; ?></p>
            </div>
            
            <h2>ক্যাটাগরি নির্বাচন করুন:</h2>
            <div class="category-list">
                <?php if (empty($categories)): ?>
                    <div class="category-card">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>কোন ক্যাটাগরি পাওয়া যায়নি</h3>
                        <p>প্রথমে ক্যাটাগরি যোগ করুন</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="category-card" onclick="selectCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">
                            <i class="fas fa-folder fa-2x"></i>
                            <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                            <p><?php echo htmlspecialchars($category['description'] ?? 'বিবরণ নেই'); ?></p>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <div id="form-section" class="form-section">
                <h2>পণ্যের তথ্য দিন:</h2>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" id="selected-category" name="category_id" value="">
                    
                    <div class="form-group">
                        <label for="product-name">পণ্যের নাম *</label>
                        <input type="text" id="product-name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-description">পণ্যের বিবরণ</label>
                        <textarea id="product-description" name="description" rows="4"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-price">দাম (৳) *</label>
                        <input type="number" id="product-price" name="price" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-stock">স্টক পরিমাণ *</label>
                        <input type="number" id="product-stock" name="stock_quantity" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-image">পণ্যের ছবি</label>
                        <input type="file" id="product-image" name="image" accept="image/*">
                    </div>
                    
                    <button type="submit" name="add_product" class="btn">
                        <i class="fas fa-plus"></i> পণ্য যোগ করুন
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function selectCategory(categoryId, categoryName) {
            // Remove previous selection
            document.querySelectorAll('.category-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked card
            event.target.closest('.category-card').classList.add('selected');
            
            // Set category ID
            document.getElementById('selected-category').value = categoryId;
            
            // Show form
            document.getElementById('form-section').classList.add('active');
            
            // Update form title
            document.querySelector('#form-section h2').textContent = `${categoryName} ক্যাটাগরিতে পণ্য যোগ করুন:`;
        }
    </script>
</body>
</html>
