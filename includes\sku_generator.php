<?php
/**
 * SKU Generator Class
 * Generates unique SKU codes for products
 */

class SKUGenerator {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Generate a unique SKU based on category and product name
     * Format: CAT-PROD-YYYYMMDD-XXXX
     * Example: ELE-PHONE-20250717-0001
     */
    public function generateSKU($categoryId, $productName) {
        // Get category info
        $stmt = $this->pdo->prepare("SELECT name FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);
        $category = $stmt->fetch();
        
        if (!$category) {
            throw new Exception('ক্যাটাগরি পাওয়া যায়নি।');
        }
        
        // Create category prefix (first 3 letters, uppercase)
        $categoryPrefix = $this->createPrefix($category['name'], 3);
        
        // Create product prefix (first 4 letters, uppercase)
        $productPrefix = $this->createPrefix($productName, 4);
        
        // Get current date
        $datePrefix = date('Ymd');
        
        // Generate base SKU
        $baseSKU = $categoryPrefix . '-' . $productPrefix . '-' . $datePrefix;
        
        // Find next available number
        $counter = $this->getNextCounter($baseSKU);
        
        // Final SKU
        $finalSKU = $baseSKU . '-' . str_pad($counter, 4, '0', STR_PAD_LEFT);
        
        return $finalSKU;
    }
    
    /**
     * Generate a simple numeric SKU
     * Format: SKU-YYYYMMDD-XXXXXX
     * Example: SKU-20250717-000001
     */
    public function generateSimpleSKU() {
        $datePrefix = date('Ymd');
        $baseSKU = 'SKU-' . $datePrefix;
        
        // Find next available number
        $counter = $this->getNextCounter($baseSKU);
        
        // Final SKU
        $finalSKU = $baseSKU . '-' . str_pad($counter, 6, '0', STR_PAD_LEFT);
        
        return $finalSKU;
    }
    
    /**
     * Generate SKU based on category only
     * Format: CAT-YYYYMMDD-XXXX
     * Example: ELE-20250717-0001
     */
    public function generateCategorySKU($categoryId) {
        // Get category info
        $stmt = $this->pdo->prepare("SELECT name FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);
        $category = $stmt->fetch();
        
        if (!$category) {
            throw new Exception('ক্যাটাগরি পাওয়া যায়নি।');
        }
        
        // Create category prefix
        $categoryPrefix = $this->createPrefix($category['name'], 3);
        
        // Get current date
        $datePrefix = date('Ymd');
        
        // Generate base SKU
        $baseSKU = $categoryPrefix . '-' . $datePrefix;
        
        // Find next available number
        $counter = $this->getNextCounter($baseSKU);
        
        // Final SKU
        $finalSKU = $baseSKU . '-' . str_pad($counter, 4, '0', STR_PAD_LEFT);
        
        return $finalSKU;
    }
    
    /**
     * Check if SKU already exists
     */
    public function skuExists($sku, $excludeId = null) {
        $sql = "SELECT id FROM products WHERE sku = ?";
        $params = [$sku];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetch() !== false;
    }
    
    /**
     * Create prefix from text (remove Bengali characters, keep English)
     */
    private function createPrefix($text, $length) {
        // Remove Bengali characters and keep only English letters
        $text = preg_replace('/[^\x00-\x7F]/', '', $text);
        
        // Remove special characters and spaces
        $text = preg_replace('/[^a-zA-Z0-9]/', '', $text);
        
        // Convert to uppercase
        $text = strtoupper($text);
        
        // If text is empty or too short, use default
        if (empty($text)) {
            $text = 'PROD';
        }
        
        // Take first N characters
        return substr($text, 0, $length);
    }
    
    /**
     * Get next available counter for a base SKU
     */
    private function getNextCounter($baseSKU) {
        // Find existing SKUs with this base
        $stmt = $this->pdo->prepare("SELECT sku FROM products WHERE sku LIKE ? ORDER BY sku DESC LIMIT 1");
        $stmt->execute([$baseSKU . '%']);
        $lastSKU = $stmt->fetch();
        
        if (!$lastSKU) {
            return 1; // First SKU with this base
        }
        
        // Extract counter from last SKU
        $parts = explode('-', $lastSKU['sku']);
        $lastCounter = intval(end($parts));
        
        return $lastCounter + 1;
    }
    
    /**
     * Generate multiple SKU options for user to choose
     */
    public function generateSKUOptions($categoryId, $productName) {
        $options = [];
        
        try {
            // Option 1: Category + Product + Date
            $options['detailed'] = [
                'sku' => $this->generateSKU($categoryId, $productName),
                'label' => 'বিস্তারিত SKU (ক্যাটাগরি + পণ্য + তারিখ)'
            ];
        } catch (Exception $e) {
            // Skip if error
        }
        
        try {
            // Option 2: Category + Date only
            $options['category'] = [
                'sku' => $this->generateCategorySKU($categoryId),
                'label' => 'ক্যাটাগরি SKU (ক্যাটাগরি + তারিখ)'
            ];
        } catch (Exception $e) {
            // Skip if error
        }
        
        // Option 3: Simple numeric
        $options['simple'] = [
            'sku' => $this->generateSimpleSKU(),
            'label' => 'সাধারণ SKU (তারিখ + নাম্বার)'
        ];
        
        return $options;
    }
}
?>
