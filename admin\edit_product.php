<?php
// Clean version of edit product page

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include required files
require_once '../includes/function_loader.php';
require_once '../includes/image_handler.php';
require_once '../includes/sku_generator.php';

// Start session
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=dokan_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get product ID
$productId = safe_get_param('id');
if (!$productId) {
    header('Location: products.php');
    exit;
}

// Get product details
try {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        header('Location: products.php');
        exit;
    }
} catch(PDOException $e) {
    die('Error fetching product: ' . $e->getMessage());
}

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch(PDOException $e) {
    die('Error fetching categories: ' . $e->getMessage());
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && safe_post('action') === 'update_product') {
    try {
        $name = trim(safe_post('name'));
        $description = trim(safe_post('description'));
        $price = floatval(safe_post('price', 0));
        $stock_quantity = intval(safe_post('stock_quantity', 0));
        $category_id = intval(safe_post('category_id', 0));
        $sku = trim(safe_post('sku'));
        $status = safe_post('status', 'active');

        // Auto-generate SKU if empty
        if (empty($sku)) {
            $skuGenerator = new SKUGenerator($pdo);
            $sku = $skuGenerator->generateSKU($category_id, $name);
        }
        
        // Basic validation
        if (empty($name) || $price <= 0 || empty($category_id)) {
            throw new Exception('নাম, দাম এবং ক্যাটাগরি আবশ্যক।');
        }

        // Handle image deletion
        $imageName = $product['image']; // Keep existing image by default
        if (isset($_POST['delete_image']) && $_POST['delete_image'] == '1') {
            // Delete existing image
            if ($product['image'] && file_exists('../uploads/products/' . $product['image'])) {
                unlink('../uploads/products/' . $product['image']);
            }
            $imageName = null; // Set to null to remove from database
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $errors = ImageHandler::validateImage($_FILES['image']);
            if (!empty($errors)) {
                throw new Exception('ছবি আপলোড এরর: ' . implode(', ', $errors));
            }

            $newImageName = ImageHandler::generateUniqueFilename($_FILES['image']['name'], 'product_');
            $destination = '../uploads/products/' . $newImageName;

            if (!ImageHandler::moveUploadedFile($_FILES['image'], $destination)) {
                throw new Exception('ছবি সংরক্ষণে সমস্যা হয়েছে।');
            }

            // Resize image if needed
            $extension = strtolower(pathinfo($newImageName, PATHINFO_EXTENSION));
            if ($extension !== 'svg') {
                $resizedPath = '../uploads/products/resized_' . $newImageName;
                if (ImageHandler::resizeImage($destination, $resizedPath, 800, 600)) {
                    unlink($destination);
                    $newImageName = 'resized_' . $newImageName;
                }
            }

            // Delete old image if exists (only if not already deleted by delete_image option)
            if ($product['image'] && file_exists('../uploads/products/' . $product['image']) && !isset($_POST['delete_image'])) {
                unlink('../uploads/products/' . $product['image']);
            }

            $imageName = $newImageName;
        }

        // Update product
        $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, stock_quantity = ?, category_id = ?, sku = ?, image = ?, status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$name, $description, $price, $stock_quantity, $category_id, $sku, $imageName, $status, $productId]);
        
        $message = 'পণ্য সফলভাবে আপডেট করা হয়েছে!';
        $messageType = 'success';
        
        // Refresh product data
        $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch();
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য সম্পাদনা - <?php echo htmlspecialchars($product['name']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e0e0;
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .current-image {
            margin-top: 10px;
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .current-image img {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        input[type="file"] {
            padding: 8px 12px;
            background: #f8f9fa;
        }

        input[type="file"]:focus {
            background: white;
        }

        .delete-image-option {
            margin-top: 10px;
            padding: 10px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            color: #856404;
        }

        .delete-image-option label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin: 0;
            font-weight: 500;
        }

        .delete-image-option input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
            transform: translateY(-2px);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .sku-option {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sku-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .sku-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .sku-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .sku-label {
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .current-image img {
                max-width: 150px;
                max-height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>পণ্য সম্পাদনা</h1>
            <p><?php echo htmlspecialchars($product['name']); ?></p>
        </div>

        <div class="main-content">
            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <form method="POST" id="editProductForm" enctype="multipart/form-data">
                <input type="hidden" name="action" value="update_product">

                <div class="form-group">
                    <label for="name">পণ্যের নাম <span class="required">*</span></label>
                    <input type="text" id="name" name="name" class="form-control" required value="<?php echo htmlspecialchars($product['name']); ?>">
                </div>

                <div class="form-group">
                    <label for="sku">SKU</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="sku" name="sku" class="form-control" value="<?php echo htmlspecialchars($product['sku'] ?? ''); ?>" placeholder="অটো জেনারেট হবে">
                        <button type="button" id="generate-sku" class="btn btn-secondary" style="white-space: nowrap;">
                            <i class="fas fa-magic"></i> জেনারেট
                        </button>
                        <button type="button" id="sku-options" class="btn btn-info" style="white-space: nowrap;">
                            <i class="fas fa-list"></i> অপশন
                        </button>
                    </div>
                    <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                        খালি রাখলে অটোমেটিক জেনারেট হবে
                    </small>
                </div>

                <div class="form-group">
                    <label for="description">বিবরণ</label>
                    <textarea id="description" name="description" class="form-control" rows="4"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                </div>

                <!-- Current Image Display -->
                <?php if ($product['image']): ?>
                <div class="form-group">
                    <label>বর্তমান ছবি</label>
                    <div class="current-image">
                        <img src="../uploads/products/<?php echo htmlspecialchars($product['image']); ?>"
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             style="max-width: 200px; max-height: 200px; border-radius: 10px; border: 2px solid #e0e0e0;">
                        <div class="delete-image-option">
                            <label>
                                <input type="checkbox" name="delete_image" value="1">
                                বর্তমান ছবি মুছে ফেলুন
                            </label>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Image Upload -->
                <div class="form-group">
                    <label for="image">নতুন ছবি আপলোড করুন</label>
                    <input type="file" id="image" name="image" class="form-control" accept="image/*">
                    <small style="color: #666; font-size: 14px;">
                        সমর্থিত ফরম্যাট: JPG, JPEG, PNG, GIF, SVG | সর্বোচ্চ সাইজ: 5MB
                    </small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="category_id">ক্যাটাগরি <span class="required">*</span></label>
                        <select id="category_id" name="category_id" class="form-control" required>
                            <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo ($category['id'] == $product['category_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="status">স্ট্যাটাস</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active" <?php echo ($product['status'] == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                            <option value="inactive" <?php echo ($product['status'] == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="price">দাম (৳) <span class="required">*</span></label>
                        <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" required value="<?php echo $product['price']; ?>">
                    </div>

                    <div class="form-group">
                        <label for="stock_quantity">স্টক পরিমাণ</label>
                        <input type="number" id="stock_quantity" name="stock_quantity" class="form-control" min="0" value="<?php echo $product['stock_quantity']; ?>">
                    </div>
                </div>

                <div class="form-actions">
                    <a href="products.php" class="btn btn-secondary">বাতিল</a>
                    <button type="submit" class="btn btn-primary">পরিবর্তন সংরক্ষণ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- SKU Options Modal -->
    <div id="skuModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-magic"></i> SKU অপশন নির্বাচন করুন</h3>
                <span class="close" id="closeSKUModal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="skuOptionsContainer">
                    <!-- SKU options will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelSKU">বাতিল</button>
                <button type="button" class="btn btn-primary" id="selectSKU">নির্বাচন করুন</button>
            </div>
        </div>
    </div>

    <script>
        // Form validation
        document.getElementById('editProductForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const price = document.getElementById('price').value;
            const categoryId = document.getElementById('category_id').value;

            if (!name) {
                e.preventDefault();
                alert('পণ্যের নাম আবশ্যক।');
                document.getElementById('name').focus();
                return;
            }

            if (!price || parseFloat(price) <= 0) {
                e.preventDefault();
                alert('সঠিক দাম দিন।');
                document.getElementById('price').focus();
                return;
            }

            if (!categoryId) {
                e.preventDefault();
                alert('ক্যাটাগরি নির্বাচন করুন।');
                document.getElementById('category_id').focus();
                return;
            }
        });

        // Generate SKU
        document.getElementById('generate-sku').addEventListener('click', function() {
            const categoryId = document.getElementById('category_id').value;
            const productName = document.getElementById('name').value.trim();

            if (!categoryId) {
                alert('প্রথমে একটি ক্যাটাগরি নির্বাচন করুন।');
                return;
            }

            if (!productName) {
                alert('প্রথমে পণ্যের নাম লিখুন।');
                document.getElementById('name').focus();
                return;
            }

            // Show loading
            const btn = this;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> জেনারেট হচ্ছে...';
            btn.disabled = true;

            // Generate SKU via AJAX
            fetch('generate_sku.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'category_id=' + encodeURIComponent(categoryId) + '&product_name=' + encodeURIComponent(productName)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('sku').value = data.sku;
                } else {
                    alert('SKU জেনারেট করতে সমস্যা হয়েছে: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('SKU জেনারেট করতে সমস্যা হয়েছে।');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        });

        // SKU Options Modal
        let selectedSKU = '';

        document.getElementById('sku-options').addEventListener('click', function() {
            const categoryId = document.getElementById('category_id').value;
            const productName = document.getElementById('name').value.trim();

            if (!categoryId) {
                alert('প্রথমে একটি ক্যাটাগরি নির্বাচন করুন।');
                return;
            }

            if (!productName) {
                alert('প্রথমে পণ্যের নাম লিখুন।');
                document.getElementById('name').focus();
                return;
            }

            // Show modal
            document.getElementById('skuModal').style.display = 'flex';

            // Load SKU options
            const container = document.getElementById('skuOptionsContainer');
            container.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';

            fetch('sku_options.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'category_id=' + encodeURIComponent(categoryId) + '&product_name=' + encodeURIComponent(productName)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '';
                    for (const [key, option] of Object.entries(data.options)) {
                        html += `
                            <div class="sku-option" data-sku="${option.sku}">
                                <div class="sku-code">${option.sku}</div>
                                <div class="sku-label">${option.label}</div>
                            </div>
                        `;
                    }
                    container.innerHTML = html;

                    // Add click handlers
                    document.querySelectorAll('.sku-option').forEach(option => {
                        option.addEventListener('click', function() {
                            document.querySelectorAll('.sku-option').forEach(o => o.classList.remove('selected'));
                            this.classList.add('selected');
                            selectedSKU = this.dataset.sku;
                        });
                    });
                } else {
                    container.innerHTML = '<div style="text-align: center; color: red;">এরর: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = '<div style="text-align: center; color: red;">SKU অপশন লোড করতে সমস্যা হয়েছে।</div>';
            });
        });

        // Modal close handlers
        document.getElementById('closeSKUModal').addEventListener('click', function() {
            document.getElementById('skuModal').style.display = 'none';
        });

        document.getElementById('cancelSKU').addEventListener('click', function() {
            document.getElementById('skuModal').style.display = 'none';
        });

        document.getElementById('selectSKU').addEventListener('click', function() {
            if (selectedSKU) {
                document.getElementById('sku').value = selectedSKU;
                document.getElementById('skuModal').style.display = 'none';
            } else {
                alert('একটি SKU নির্বাচন করুন।');
            }
        });

        // Close modal on outside click
        document.getElementById('skuModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // Image preview functionality
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
                if (!allowedTypes.includes(file.type)) {
                    alert('অনুগ্রহ করে একটি বৈধ ছবি ফাইল নির্বাচন করুন (JPG, PNG, GIF, SVG)।');
                    e.target.value = '';
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('ছবির সাইজ ৫MB এর কম হতে হবে।');
                    e.target.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Remove existing preview if any
                    const existingPreview = document.querySelector('.image-preview');
                    if (existingPreview) {
                        existingPreview.remove();
                    }

                    // Create new preview
                    const preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.style.marginTop = '10px';
                    preview.style.textAlign = 'center';
                    preview.style.padding = '15px';
                    preview.style.background = '#f0f8ff';
                    preview.style.borderRadius = '10px';
                    preview.style.border = '2px dashed #667eea';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.maxWidth = '200px';
                    img.style.maxHeight = '200px';
                    img.style.borderRadius = '10px';
                    img.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';

                    const label = document.createElement('p');
                    label.textContent = 'নতুন ছবি প্রিভিউ';
                    label.style.margin = '10px 0 0 0';
                    label.style.color = '#667eea';
                    label.style.fontWeight = '600';

                    preview.appendChild(img);
                    preview.appendChild(label);

                    // Insert preview after the file input
                    document.getElementById('image').parentNode.appendChild(preview);
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
