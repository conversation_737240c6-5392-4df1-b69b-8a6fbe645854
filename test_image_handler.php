<?php
require_once 'includes/image_handler.php';

echo "<h2>Image Handler Test</h2>";

// Test supported formats
echo "<h3>Supported Formats:</h3>";
$testFiles = [
    'test.jpg' => 'JPG',
    'test.jpeg' => 'JPEG', 
    'test.png' => 'PNG',
    'test.gif' => 'GIF',
    'test.webp' => 'WebP',
    'test.bmp' => 'BMP',
    'test.svg' => 'SVG',
    'test.ico' => 'ICO',
    'test.tiff' => 'TIFF',
    'test.tif' => 'TIF',
    'test.txt' => 'TXT (should fail)'
];

foreach ($testFiles as $filename => $format) {
    $isSupported = ImageHandler::isSupportedFormat($filename);
    $status = $isSupported ? '✅ Supported' : '❌ Not Supported';
    echo "<p><strong>$format ($filename):</strong> $status</p>";
}

// Test MIME types
echo "<h3>MIME Type Validation:</h3>";
$testMimes = [
    'image/jpeg' => 'JPEG',
    'image/png' => 'PNG', 
    'image/gif' => 'GIF',
    'image/webp' => 'WebP',
    'image/bmp' => 'BMP',
    'image/svg+xml' => 'SVG',
    'image/x-icon' => 'ICO',
    'image/tiff' => 'TIFF',
    'text/plain' => 'Plain Text (should fail)'
];

foreach ($testMimes as $mime => $format) {
    // We need to use reflection to test private method
    $reflection = new ReflectionClass('ImageHandler');
    $method = $reflection->getMethod('isValidImageMime');
    $method->setAccessible(true);
    
    $isValid = $method->invoke(null, $mime);
    $status = $isValid ? '✅ Valid' : '❌ Invalid';
    echo "<p><strong>$format ($mime):</strong> $status</p>";
}

// Test file generation
echo "<h3>Filename Generation Test:</h3>";
$testFilename = ImageHandler::generateUniqueFilename('test image.png', 'product_');
echo "<p><strong>Original:</strong> test image.png</p>";
echo "<p><strong>Generated:</strong> $testFilename</p>";

// Check if uploads directory exists
echo "<h3>Upload Directory Check:</h3>";
$uploadDir = 'uploads/products/';
if (is_dir($uploadDir)) {
    echo "<p>✅ Upload directory exists: $uploadDir</p>";
    if (is_writable($uploadDir)) {
        echo "<p>✅ Upload directory is writable</p>";
    } else {
        echo "<p>❌ Upload directory is not writable</p>";
    }
} else {
    echo "<p>❌ Upload directory does not exist: $uploadDir</p>";
}

// Check GD extension
echo "<h3>GD Extension Check:</h3>";
if (extension_loaded('gd')) {
    echo "<p>✅ GD extension is loaded</p>";
    $gdInfo = gd_info();
    echo "<ul>";
    foreach ($gdInfo as $key => $value) {
        if (is_bool($value)) {
            $value = $value ? 'Yes' : 'No';
        }
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ GD extension is not loaded</p>";
}

echo "<h3>Function Availability:</h3>";
$functions = [
    'imagecreatefromjpeg' => 'JPEG support',
    'imagecreatefrompng' => 'PNG support', 
    'imagecreatefromgif' => 'GIF support',
    'imagecreatefromwebp' => 'WebP read support',
    'imagewebp' => 'WebP write support',
    'imagecreatefrombmp' => 'BMP read support',
    'imagebmp' => 'BMP write support'
];

foreach ($functions as $func => $desc) {
    $exists = function_exists($func);
    $status = $exists ? '✅ Available' : '❌ Not Available';
    echo "<p><strong>$desc ($func):</strong> $status</p>";
}
?>
