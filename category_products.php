<?php
require_once 'config/database.php';
require_once 'config/functions.php';

// Get category ID from URL
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12; // Products per page
$offset = ($page - 1) * $limit;

// Get all categories for navigation
try {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch(PDOException $e) {
    $categories = [];
}

// Get selected category info and products
$selectedCategory = null;
$products = [];
$totalProducts = 0;
$totalPages = 0;

if ($category_id) {
    try {
        // Get category info
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ? AND status = 'active'");
        $stmt->execute([$category_id]);
        $selectedCategory = $stmt->fetch();
        
        if ($selectedCategory) {
            // Get products count
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ? AND status = 'active'");
            $stmt->execute([$category_id]);
            $totalProducts = $stmt->fetchColumn();
            $totalPages = ceil($totalProducts / $limit);
            
            // Get products
            $stmt = $pdo->prepare("SELECT p.*, c.name as category_name 
                                  FROM products p 
                                  LEFT JOIN categories c ON p.category_id = c.id 
                                  WHERE p.category_id = ? AND p.status = 'active' 
                                  ORDER BY p.created_at DESC 
                                  LIMIT ? OFFSET ?");
            $stmt->execute([$category_id, $limit, $offset]);
            $products = $stmt->fetchAll();
        }
    } catch(PDOException $e) {
        $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $selectedCategory ? $selectedCategory['name'] . ' - ' : ''; ?>ক্যাটাগরি অনুযায়ী পণ্য | দোকান</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .category-nav {
            background: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .category-nav h2 {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #333;
            font-size: 1.8rem;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            padding: 0 1rem;
        }
        
        .category-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            color: white;
        }
        
        .category-card.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            transform: scale(1.05);
        }
        
        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .category-card:hover::before {
            left: 100%;
        }
        
        .category-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .category-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .category-count {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .selected-category {
            background: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .selected-category h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .selected-category p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .category-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .no-category {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .no-category i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }
        
        .no-category h2 {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .no-category p {
            color: #999;
        }
        
        .products-section {
            margin-top: 2rem;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin: 0;
        }
        
        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .view-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .products-list {
            display: none;
        }
        
        .products-list.active {
            display: block;
        }
        
        .products-grid.list-view {
            grid-template-columns: 1fr;
        }
        
        .product-card.list-view {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .product-card.list-view .product-image-container {
            width: 120px;
            height: 120px;
            flex-shrink: 0;
        }
        
        .product-card.list-view .product-info {
            flex: 1;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        @media (max-width: 768px) {
            .category-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .category-stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .section-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <main class="main-content">
        <div class="container">
            <!-- Category Navigation -->
            <section class="category-nav">
                <h2><i class="fas fa-th-large"></i> পণ্যের ক্যাটাগরি</h2>
                <div class="category-grid">
                    <?php 
                    $categoryIcons = [
                        'ইলেকট্রনিক্স' => 'fas fa-laptop',
                        'ফ্যাশন' => 'fas fa-tshirt',
                        'বই' => 'fas fa-book',
                        'খাবার' => 'fas fa-utensils',
                        'স্বাস্থ্য ও সৌন্দর্য' => 'fas fa-heart',
                        'গৃহস্থালী' => 'fas fa-home',
                        'খেলাধুলা' => 'fas fa-football-ball',
                        'শিশুদের পণ্য' => 'fas fa-baby',
                        'অটোমোবাইল' => 'fas fa-car',
                        'বাগান ও বহিরঙ্গন' => 'fas fa-leaf'
                    ];
                    
                    foreach ($categories as $category): 
                        $icon = $categoryIcons[$category['name']] ?? 'fas fa-box';
                        $isActive = ($category_id == $category['id']) ? 'active' : '';
                        
                        // Get product count for this category
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ? AND status = 'active'");
                        $stmt->execute([$category['id']]);
                        $productCount = $stmt->fetchColumn();
                    ?>
                        <a href="?category=<?php echo $category['id']; ?>" class="category-card <?php echo $isActive; ?>">
                            <i class="category-icon <?php echo $icon; ?>"></i>
                            <div class="category-name"><?php echo htmlspecialchars($category['name']); ?></div>
                            <div class="category-count"><?php echo $productCount; ?>টি পণ্য</div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </section>

            <?php if ($selectedCategory): ?>
                <!-- Selected Category Info -->
                <section class="selected-category">
                    <h1><?php echo htmlspecialchars($selectedCategory['name']); ?></h1>
                    <p><?php echo htmlspecialchars($selectedCategory['description'] ?: 'এই ক্যাটাগরিতে বিভিন্ন ধরনের পণ্য রয়েছে।'); ?></p>
                    <div class="category-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $totalProducts; ?></span>
                            <span class="stat-label">মোট পণ্য</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $totalPages; ?></span>
                            <span class="stat-label">পৃষ্ঠা</span>
                        </div>
                    </div>
                </section>

                <?php if (!empty($products)): ?>
                    <!-- Products Section -->
                    <section class="products-section">
                        <div class="section-header">
                            <h3 class="section-title"><?php echo htmlspecialchars($selectedCategory['name']); ?> এর পণ্যসমূহ</h3>
                            <div class="view-toggle">
                                <button class="view-btn active" onclick="toggleView('grid')">
                                    <i class="fas fa-th"></i> গ্রিড
                                </button>
                                <button class="view-btn" onclick="toggleView('list')">
                                    <i class="fas fa-list"></i> তালিকা
                                </button>
                            </div>
                        </div>

                        <div class="products-grid" id="products-container">
                            <?php foreach ($products as $product): ?>
                                <div class="product-card">
                                    <div class="product-image-container">
                                        <?php 
                                        $imageSrc = $product['image'] ? 'uploads/products/' . $product['image'] : 'assets/images/no-image.svg';
                                        echo '<img src="' . $imageSrc . '" alt="' . htmlspecialchars($product['name']) . '" class="product-image" data-image-src="' . $imageSrc . '" data-product-name="' . htmlspecialchars($product['name']) . '">';
                                        ?>
                                        <div class="stock-badge <?php echo $product['stock_quantity'] > 0 ? 'in-stock' : 'out-of-stock'; ?>">
                                            স্টক: <?php echo $product['stock_quantity']; ?>
                                        </div>
                                    </div>
                                    <div class="product-info">
                                        <h4 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h4>
                                        <div class="product-price">
                                            <?php if ($product['discount_price'] && $product['discount_price'] < $product['price']): ?>
                                                <span class="original-price"><?php echo format_price($product['price']); ?></span>
                                                <span class="discounted-price"><?php echo format_price($product['discount_price']); ?></span>
                                            <?php else: ?>
                                                <span class="current-price"><?php echo format_price($product['price']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="product-actions">
                                            <a href="product_details.php?id=<?php echo $product['id']; ?>" class="btn btn-primary">
                                                <i class="fas fa-eye"></i> বিস্তারিত
                                            </a>
                                            <button onclick="addToCart(<?php echo $product['id']; ?>)" class="btn btn-secondary">
                                                <i class="fas fa-shopping-cart"></i> কার্টে যোগ করুন
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?category=<?php echo $category_id; ?>&page=<?php echo $page - 1; ?>">
                                        <i class="fas fa-chevron-left"></i> পূর্ববর্তী
                                    </a>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <?php if ($i == $page): ?>
                                        <span class="current"><?php echo $i; ?></span>
                                    <?php else: ?>
                                        <a href="?category=<?php echo $category_id; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?category=<?php echo $category_id; ?>&page=<?php echo $page + 1; ?>">
                                        পরবর্তী <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </section>
                <?php else: ?>
                    <div class="no-category">
                        <i class="fas fa-box-open"></i>
                        <h2>কোন পণ্য পাওয়া যায়নি</h2>
                        <p>এই ক্যাটাগরিতে এখনো কোন পণ্য যোগ করা হয়নি।</p>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="no-category">
                    <i class="fas fa-th-large"></i>
                    <h2>একটি ক্যাটাগরি নির্বাচন করুন</h2>
                    <p>উপরের ক্যাটাগরিগুলি থেকে একটি নির্বাচন করে সেই ক্যাটাগরির পণ্যসমূহ দেখুন।</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function toggleView(view) {
            const container = document.getElementById('products-container');
            const buttons = document.querySelectorAll('.view-btn');
            
            // Remove active class from all buttons
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (view === 'list') {
                container.classList.add('list-view');
                container.querySelectorAll('.product-card').forEach(card => {
                    card.classList.add('list-view');
                });
                document.querySelector('.view-btn:last-child').classList.add('active');
            } else {
                container.classList.remove('list-view');
                container.querySelectorAll('.product-card').forEach(card => {
                    card.classList.remove('list-view');
                });
                document.querySelector('.view-btn:first-child').classList.add('active');
            }
        }

        // Add to cart function
        function addToCart(productId) {
            $.ajax({
                url: 'api/add_to_cart.php',
                method: 'POST',
                data: {
                    product_id: productId,
                    quantity: 1
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        updateCartCount();
                        showNotification('পণ্যটি কার্টে যোগ করা হয়েছে!', 'success');
                    } else {
                        showNotification(response.message || 'কার্টে যোগ করতে সমস্যা হয়েছে।', 'error');
                    }
                },
                error: function() {
                    showNotification('সার্ভার এরর। পরে আবার চেষ্টা করুন।', 'error');
                }
            });
        }

        // Show notification
        function showNotification(message, type) {
            const notification = $(`
                <div class="notification ${type}" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#4CAF50' : '#f44336'};
                    color: white;
                    padding: 1rem 1.5rem;
                    border-radius: 5px;
                    z-index: 10000;
                    animation: slideIn 0.3s ease;
                ">
                    ${message}
                </div>
            `);
            
            $('body').append(notification);
            
            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 3000);
        }

        $(document).ready(function() {
            updateCartCount();
            
            // Add click event listeners to product images
            $('.product-image').on('click', function() {
                const imageSrc = $(this).data('image-src');
                const productName = $(this).data('product-name');
                if (imageSrc && productName) {
                    showImagePreview(imageSrc, productName);
                }
            });
        });
    </script>
</body>
</html>
