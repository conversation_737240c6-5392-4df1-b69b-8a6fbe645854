/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Ensure all headings use Hind Siliguri */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    font-weight: 600;
}

/* Ensure all text elements use Hind Siliguri */
p, span, div, a, li, td, th, label, input, textarea, select, button {
    font-family: 'Hind Siliguri', Arial, sans-serif;
}

/* Special class for Bengali text */
.bengali-text {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    font-weight: 400;
}

/* Override any conflicting font declarations */
* {
    font-family: 'Hind Siliguri', <PERSON>l, sans-serif !important;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 1.8rem;
    font-weight: 600;
    font-family: 'Hind Siliguri', Arial, sans-serif;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #ffd700;
}

#cart-count {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8rem;
    margin-left: 5px;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 4rem 0;
    margin-bottom: 3rem;
    border-radius: 10px;
}

.hero-content h2 {
    font-size: 2.5rem;
    font-weight: 600;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.2rem;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: 2px solid #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #2ed573;
    color: white;
}

.btn-success:hover {
    background: #26d467;
}

.btn-danger {
    background: #ff4757;
    color: white;
}

.btn-danger:hover {
    background: #ff3838;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Grid Layouts */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Categories Section */
.categories {
    margin-bottom: 4rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h3 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
    font-weight: 700;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.categories-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.category-card i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.category-card:hover i {
    transform: scale(1.1) rotate(5deg);
}

.category-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
    transition: color 0.3s ease;
}

.category-card:hover h4 {
    color: #667eea;
}

.category-card p {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    margin: 0;
}

/* New Product Badge */
.new-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 0.5rem 0.8rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    transform: rotate(8deg);
    animation: pulse 2s infinite;
    z-index: 10;
    min-width: 80px;
}

.new-badge .new-text {
    display: block;
    font-size: 0.8rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.new-badge .new-subtext {
    display: block;
    font-size: 0.65rem;
    opacity: 0.9;
    font-weight: 500;
}

.category-card.has-new {
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% {
        transform: rotate(8deg) scale(1);
    }
    50% {
        transform: rotate(8deg) scale(1.05);
    }
    100% {
        transform: rotate(8deg) scale(1);
    }
}

@keyframes glow {
    from {
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }
    to {
        box-shadow: 0 5px 20px rgba(255, 107, 107, 0.2), 0 15px 40px rgba(0,0,0,0.15);
    }
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image-container {
    position: relative;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 10px;
}

.product-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    filter: brightness(1.1);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.product-image-container::after {
    content: '🔍';
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px;
    border-radius: 50%;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.product-image-container:hover::after {
    opacity: 1;
}

/* Image Preview Modal */
.image-preview-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.image-preview-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    animation: zoomIn 0.3s ease;
}

.image-preview-content img {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.image-preview-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 30px;
    font-weight: bold;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.image-preview-close:hover {
    background: rgba(255, 0, 0, 0.7);
}

.image-preview-info {
    position: absolute;
    bottom: -60px;
    left: 0;
    right: 0;
    color: white;
    text-align: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    font-family: 'Hind Siliguri', sans-serif;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile Responsive for Image Preview */
@media (max-width: 768px) {
    .image-preview-content {
        max-width: 95%;
        max-height: 95%;
    }

    .image-preview-close {
        top: -35px;
        font-size: 25px;
        width: 35px;
        height: 35px;
    }

    .image-preview-info {
        bottom: -50px;
        padding: 8px;
        font-size: 14px;
    }

    .product-image-container::after {
        font-size: 12px;
        padding: 6px;
    }
}

/* Stock Badge */
.stock-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    color: white;
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
    z-index: 10;
    display: block;
    min-width: 60px;
    text-align: center;
}

.stock-badge.stock-available {
    background: #28a745;
    border: 2px solid #fff;
}

.stock-badge.stock-low {
    background: #ffc107;
    color: #333;
    border: 2px solid #fff;
}

.stock-badge.stock-out {
    background: #dc3545;
    border: 2px solid #fff;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    color: #667eea;
    margin-bottom: 1rem;
}

.product-price .original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

/* Category Cards */
.category-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
}

.category-card h4 {
    color: #333;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    color: #333;
}

.table td {
    font-family: 'Hind Siliguri', Arial, sans-serif;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    font-family: 'Hind Siliguri', Arial, sans-serif;
    font-weight: 600;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffd700;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .section-header h3 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .categories-container {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
    }

    .category-card {
        padding: 1.5rem 1rem;
    }

    .category-card i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .category-card h4 {
        font-size: 1.1rem;
    }

    .category-card p {
        font-size: 0.85rem;
    }
}

/* Loading Animation */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
    padding: 12px 20px;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: 4px;
    text-transform: uppercase;
}

.badge-pending {
    background: #ffeaa7;
    color: #d63031;
}

.badge-confirmed {
    background: #74b9ff;
    color: white;
}

.badge-processing {
    background: #fd79a8;
    color: white;
}

.badge-shipped {
    background: #fdcb6e;
    color: #2d3436;
}

.badge-delivered {
    background: #00b894;
    color: white;
}

.badge-cancelled {
    background: #636e72;
    color: white;
}

.badge-warning {
    background: #fdcb6e;
    color: #2d3436;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-close:hover {
    color: #333;
}
