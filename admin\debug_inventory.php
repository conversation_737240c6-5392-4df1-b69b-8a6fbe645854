<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';

echo "<h1>Inventory Debug Information</h1>";

// Check database connection
echo "<h2>Database Connection</h2>";
if ($pdo) {
    echo "✅ Database connected successfully<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// Check categories
echo "<h2>Categories</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY id");
    $categories = $stmt->fetchAll();
    echo "Total categories: " . count($categories) . "<br>";
    
    if (empty($categories)) {
        echo "❌ No categories found. Adding sample categories...<br>";
        
        // Add sample categories
        $sampleCategories = [
            ['ইলেকট্রনিক্স', 'মোবাইল, ল্যাপটপ, টিভি ইত্যাদি'],
            ['পোশাক', 'শার্ট, প্যান্ট, কুর্তি ইত্যাদি'],
            ['বই', 'উপন্যাস, গল্প, শিক্ষামূলক বই'],
            ['খাবার', 'চাল, ডাল, তেল, মসলা'],
            ['ঔষধ', 'ট্যাবলেট, সিরাপ, মলম']
        ];
        
        foreach ($sampleCategories as $cat) {
            $stmt = $pdo->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')");
            $stmt->execute($cat);
        }
        
        echo "✅ Sample categories added<br>";
        
        // Reload categories
        $stmt = $pdo->query("SELECT * FROM categories ORDER BY id");
        $categories = $stmt->fetchAll();
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Status</th><th>Created</th></tr>";
    foreach ($categories as $cat) {
        echo "<tr>";
        echo "<td>" . $cat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['name']) . "</td>";
        echo "<td>" . $cat['status'] . "</td>";
        echo "<td>" . ($cat['created_at'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Error checking categories: " . $e->getMessage() . "<br>";
}

// Check products
echo "<h2>Products</h2>";
try {
    $stmt = $pdo->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.created_at DESC");
    $products = $stmt->fetchAll();
    echo "Total products: " . count($products) . "<br>";
    
    if (!empty($products)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Status</th><th>Created</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category_name'] ?? 'N/A') . "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "<td>" . $product['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No products found.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking products: " . $e->getMessage() . "<br>";
}

// Check buyers
echo "<h2>Buyers</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM buyers ORDER BY created_at DESC");
    $buyers = $stmt->fetchAll();
    echo "Total buyers: " . count($buyers) . "<br>";
    
    if (!empty($buyers)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Company</th><th>Status</th><th>Created</th></tr>";
        foreach ($buyers as $buyer) {
            echo "<tr>";
            echo "<td>" . $buyer['id'] . "</td>";
            echo "<td>" . htmlspecialchars($buyer['name']) . "</td>";
            echo "<td>" . htmlspecialchars($buyer['company_name'] ?? 'N/A') . "</td>";
            echo "<td>" . $buyer['status'] . "</td>";
            echo "<td>" . $buyer['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No buyers found.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking buyers: " . $e->getMessage() . "<br>";
}

// Add test data button
echo "<h2>Quick Actions</h2>";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_test_data'])) {
        try {
            // Add test product
            $name = 'Test Product ' . date('H:i:s');
            $description = 'Test product for debugging';
            $category_id = 1; // First category
            $sku = 'TEST-' . time();
            
            $stmt = $pdo->prepare("INSERT INTO products (name, description, category_id, sku, price, stock_quantity, status, created_at) VALUES (?, ?, ?, ?, 0, 0, 'draft', NOW())");
            $result = $stmt->execute([$name, $description, $category_id, $sku]);
            
            if ($result) {
                echo "✅ Test product added: $name<br>";
            } else {
                echo "❌ Failed to add test product<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ Error adding test data: " . $e->getMessage() . "<br>";
        }
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='add_test_data' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Add Test Product</button>";
echo "</form>";

echo "<br><a href='inventory_management.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Back to Inventory Management</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    padding: 20px;
    background: #f8f9fa;
}
table {
    background: white;
    width: 100%;
    max-width: 800px;
}
th, td {
    padding: 8px 12px;
    text-align: left;
}
th {
    background: #e9ecef;
}
h1, h2 {
    color: #333;
}
</style>
