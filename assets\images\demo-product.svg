<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#grad1)"/>
  
  <!-- Product Box -->
  <rect x="100" y="80" width="200" height="140" fill="#ffffff" rx="10" opacity="0.9"/>
  
  <!-- Product Icon -->
  <circle cx="200" cy="120" r="25" fill="#667eea"/>
  <text x="200" y="130" text-anchor="middle" fill="white" font-size="24">📦</text>
  
  <!-- Product Text -->
  <text x="200" y="170" text-anchor="middle" fill="#333" font-size="16" font-family="Arial, sans-serif">নমুনা পণ্য</text>
  <text x="200" y="190" text-anchor="middle" fill="#666" font-size="12" font-family="Arial, sans-serif">ছবি প্রিভিউ টেস্ট</text>
  
  <!-- Click indicator -->
  <text x="200" y="260" text-anchor="middle" fill="white" font-size="14" font-family="Arial, sans-serif">ক্লিক করে বড় করুন</text>
</svg>
