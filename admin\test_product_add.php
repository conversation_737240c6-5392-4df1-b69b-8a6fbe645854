<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_test_product'])) {
    try {
        $name = 'Test Product ' . date('Y-m-d H:i:s');
        $description = 'This is a test product';
        $category_id = 1; // Assuming category 1 exists
        $sku = 'TEST-' . time();
        
        $stmt = $pdo->prepare("INSERT INTO products (name, description, category_id, sku, price, stock_quantity, status, created_at) VALUES (?, ?, ?, ?, 0, 0, 'draft', NOW())");
        $result = $stmt->execute([$name, $description, $category_id, $sku]);
        
        if ($result) {
            $message = "Test product added successfully: $name";
        } else {
            $message = "Failed to add test product";
        }
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
    }
}

// Get all draft products
$stmt = $pdo->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'draft' ORDER BY p.created_at DESC");
$draft_products = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Add</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .product-list {
            margin-top: 30px;
        }
        .product-item {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Product Add</h1>
        
        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <button type="submit" name="add_test_product" class="btn">Add Test Product</button>
        </form>
        
        <div class="product-list">
            <h2>Draft Products (<?php echo count($draft_products); ?>)</h2>
            
            <?php if (empty($draft_products)): ?>
                <p>No draft products found.</p>
            <?php else: ?>
                <?php foreach ($draft_products as $product): ?>
                    <div class="product-item">
                        <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                        <p><strong>Category:</strong> <?php echo htmlspecialchars($product['category_name'] ?? 'N/A'); ?></p>
                        <p><strong>SKU:</strong> <?php echo htmlspecialchars($product['sku']); ?></p>
                        <p><strong>Created:</strong> <?php echo $product['created_at']; ?></p>
                        <p><strong>Status:</strong> <?php echo $product['status']; ?></p>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="inventory_management.php" class="btn">Back to Inventory Management</a>
        </div>
    </div>
</body>
</html>
